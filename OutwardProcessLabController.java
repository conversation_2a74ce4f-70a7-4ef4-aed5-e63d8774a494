package org.dromara.tianxin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.ObjectUtil;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.tianxin.domain.bo.ProcessLabBo;
import org.dromara.tianxin.domain.vo.ProcessLabVo;
import org.dromara.tianxin.service.IProcessLabService;
import org.dromara.tianxin.service.IEmailService;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 外发加工标签信息控制器
 * 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等
 *
 * <AUTHOR>
 * @date 2024-12-16
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/tianxin/processLab")
public class OutwardProcessLabController extends BaseController {

    private final IProcessLabService processLabService;
    private final IEmailService emailService;

    /**
     * 查询外发加工标签信息列表
     */
    @SaCheckPermission("tianxin:processLab:list")
    @GetMapping("/list")
    public TableDataInfo<ProcessLabVo> list(ProcessLabBo bo, PageQuery pageQuery) {
        return processLabService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取外发数据列表
     */
    @SaCheckPermission("tianxin:processLab:list")
    @GetMapping("/getOutSourceList")
    public R<List<ProcessLabVo>> getOutSourceList(ProcessLabBo bo) {
        List<ProcessLabVo> list = processLabService.queryList(bo);
        return R.ok(list);
    }

    /**
     * 导出外发加工标签信息列表
     */
    @SaCheckPermission("tianxin:processLab:export")
    @Log(title = "外发加工标签信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProcessLabBo bo, HttpServletResponse response) {
        List<ProcessLabVo> list = processLabService.queryList(bo);
        ExcelUtil.exportExcel(list, "外发加工标签信息", ProcessLabVo.class, response);
    }

    /**
     * 获取外发加工标签信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("tianxin:processLab:query")
    @GetMapping("/{id}")
    public R<ProcessLabVo> getInfo(@NotNull(message = "主键不能为空")
                                   @PathVariable Long id) {
        return R.ok(processLabService.queryById(id));
    }

    /**
     * 新增外发加工标签信息
     */
    @SaCheckPermission("tianxin:processLab:add")
    @Log(title = "外发加工标签信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProcessLabBo bo) {
        return toAjax(processLabService.insertByBo(bo));
    }

    /**
     * 修改外发加工标签信息
     */
    @SaCheckPermission("tianxin:processLab:edit")
    @Log(title = "外发加工标签信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProcessLabBo bo) {
        return toAjax(processLabService.updateByBo(bo));
    }

    /**
     * 删除外发加工标签信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("tianxin:processLab:remove")
    @Log(title = "外发加工标签信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(processLabService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 发送邮件
     *
     * @param files 附件文件列表
     * @param content 邮件内容
     * @param tableData 表格数据
     * @param subject 邮件主题
     */
    @SaCheckPermission("tianxin:processLab:sendEmail")
    @Log(title = "发送外发加工标签邮件", businessType = BusinessType.OTHER)
    @PostMapping(value = "/sendEmail", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Void> sendEmail(
            @RequestParam(value = "files", required = false) MultipartFile[] files,
            @RequestParam(value = "content", required = false) String content,
            @RequestParam("tableData") String tableData,
            @RequestParam(value = "subject", required = false) String subject) {
        
        try {
            // 调用邮件服务发送邮件
            emailService.sendProcessLabEmail(files, content, tableData, subject);
            return R.ok();
        } catch (Exception e) {
            log.error("发送邮件失败", e);
            return R.fail("发送邮件失败：" + e.getMessage());
        }
    }
}
