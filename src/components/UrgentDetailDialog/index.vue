<template>
  <el-dialog v-model="dialogVisible" title="详情" width="80%">
    <el-table height="80%" border :data="localDataList" show-overflow-tooltip>
      <el-table-column label="状态" :width="widthMedium" align="center" prop="status" />
      <el-table-column label="制造部门" :width="widthMedium" align="center" prop="manufacturingDepartment" />
      <el-table-column label="SO_NO" :width="widthMedium" align="center" prop="soNo" />
      <el-table-column label="SO_ITM" align="center" prop="soItm" />
      <el-table-column label="MO_NO" :width="widthMedium" align="center" prop="moNo" />
      <el-table-column label="图号" :width="widthWide + 150" align="center" prop="drawingNumber" />
      <el-table-column label="版本" align="center" prop="version" />
      <el-table-column label="客户代码" align="center" prop="customerCode" />
      <el-table-column label="订单数量" align="center" prop="orderQuantity" />
      <el-table-column label="生产数量" align="center" prop="productionQuantity" />
      <el-table-column label="接单日期" :width="widthMedium" align="center" prop="orderDate" />
      <el-table-column label="客户要求交期" :width="widthMedium" align="center" prop="customerRequiredDeliveryDate" />
      <el-table-column label="PMC要求交期" :width="widthMedium" align="center" prop="pmcRequiredDeliveryDate" />
      <el-table-column label="入仓日期" :width="widthMedium" align="center" prop="warehouseEntryDate" />
      <el-table-column label="末工序时间" :width="widthMedium" align="center" prop="finalProcessTime" />
      <el-table-column label="延误天数" align="center" prop="delayDays" />
      <el-table-column label="客户PO号" :width="widthMedium" align="center" prop="customerPoNumber" />
      <el-table-column label="备注" align="center" prop="remarks" />
      <el-table-column label="下单员" :width="widthMedium" align="center" prop="orderTaker" />
      <el-table-column label="跟单负责人" :width="widthMedium" align="center" prop="followUpResponsiblePerson" />
      <el-table-column label="工艺编制人" :width="widthMedium" align="center" prop="processCompilationPerson" />
      <el-table-column label="BOM责任人" :width="widthMedium" align="center" prop="bomResponsiblePerson" />
      <el-table-column label="项目经理" :width="widthMedium" align="center" prop="projectManager" />
      <el-table-column label="分组时间" :width="widthMedium" align="center" prop="groupingTime" />
      <el-table-column label="利润中心" align="center" prop="profitCenter" />
      <el-table-column label="零件分类" align="center" prop="partClassification" />
      <el-table-column label="URGENT" align="center" prop="urgent" />
      <el-table-column label="订单类别" align="center" prop="orderCategory" />
      <el-table-column label="类别描述" :width="widthMedium" align="center" prop="categoryDescription" />
      <el-table-column label="计划类型" align="center" prop="planType" />
      <el-table-column label="品号" :width="widthMedium" align="center" prop="itemNumber" />
      <el-table-column label="内部订单号" :width="widthMedium" align="center" prop="internalOrderNumber" />
      <el-table-column label="上层订单号" :width="widthMedium" align="center" prop="upperOrderNumber" />
      <el-table-column label="DR_NO" :width="widthMedium" align="center" prop="drNo" />
      <el-table-column label="DR_ITM" align="center" prop="drItm" />
      <el-table-column label="动作" :width="widthMedium" align="center" prop="action" />
      <el-table-column label="生产车间" align="center" prop="productionWorkshop" />
    </el-table>

    <div class="footer">
      <div style="margin-top: 20px;padding-top: 10px">
        <el-button type="primary" :icon="Download" @click="handleExport">
          下载
        </el-button>
      </div>
      <pagination :hide-on-single-page="true" :total="props.dataList.length" v-model:page="page.pageNum"
        v-model:limit="page.pageSize" :autoScroll="false" @pagination="getDataList" @current-change="pageChange" />
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { Download } from '@element-plus/icons-vue'
const dialogVisible = defineModel<boolean>('dialogVisible')
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const props = defineProps<{
  downloadUrl: string,
  tableName: string,
  dataList: any[],
}>()
const loading = ref(false)
const localDataList = ref([])
const page = ref({
  pageNum: 1,
  pageSize: 10,
  total: 0,
})
const height = ref(600)
const widthNarrow = 100
const widthMedium = 150
const widthWide = 200

onMounted(() => {
  height.value = window.innerHeight - 200
})

watch(() => props.dataList, () => {
  getDataList()
})

const pageChange = (pageNum: number) => {
  page.value.pageNum = pageNum
}

const getDataList = () => {
  localDataList.value = props.dataList.slice(
    (page.value.pageNum - 1) * page.value.pageSize,
    page.value.pageNum * page.value.pageSize
  )
}

const handleExport = () => {
  try {
    proxy?.download(props.downloadUrl, {
      moNos: props.dataList.map(item => item.moNo).join(','),
    }, `${props.tableName}${new Date().getTime()}.xlsx`)
  } catch (error) {
    ElMessage.error('导出失败');
  }
}
</script>

<style scoped>
.footer {
  display: flex;
  justify-content: space-between;
}
</style>