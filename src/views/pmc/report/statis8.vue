<template>
  <div class="p-4">
    <!-- 搜索卡片 -->
    <el-card class="mb-6 shadow-md">
      <template #header>
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold text-red-500">【自动化】外发全工序、多工序交货准时率--【按日统计】</h3>
        </div>
      </template>

      <el-form :model="searchForm" label-width="100px" :inline="true">
        <el-row :gutter="24" class="items-center mb-6">
          <el-col :span="8">
            <el-form-item label="要求交期" class="text-red-500">
              <el-date-picker
                v-model="searchForm.deliveryDateRange"
                type="daterange"
                range-separator="--"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 260px"
              />
            </el-form-item>
          </el-col>
          <el-col :span="16" class="flex justify-end gap-3">
            <el-button type="primary" :loading="loading" @click="handleQuery" size="default">
              <el-icon>
                <Search />
              </el-icon>
              查询
            </el-button>
            <el-button @click="handleReset" size="default">
              <el-icon>
                <Refresh />
              </el-icon>
              重置
            </el-button>
            <el-button type="success" :loading="exportLoading" @click="handleExport" :disabled="isExportDisabled" size="default">
              <el-icon>
                <Download />
              </el-icon>
              导出
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 统计信息标题 -->
    <div class="mb-4">
      <span class="text-red-500 font-semibold">统计信息</span>
    </div>

    <!-- 统计表格区域 -->
    <el-card class="shadow-md">
      <el-table :data="paginatedStatisData" border stripe>
        <el-table-column prop="id" label="ID" width="60" align="center" />
        <el-table-column prop="pmcDeliveryDate" label="PMC要求交期" width="150" align="center">
          <template #default="{ row }">
            <span>
              {{ row.pmcDeliveryDate }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="expectedDeliveryCount" label="应交货数数" width="120" align="center">
          <template #default="{ row }">
            <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'expectedDeliveryCountDetails')">
              {{ row.expectedDeliveryCount }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="actualDeliveryCount" label="已交货数数" width="120" align="center">
          <template #default="{ row }">
            <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'actualDeliveryCountDetails')">
              {{ row.actualDeliveryCount }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="onTimeDeliveryCount" label="准时交货数数" width="130" align="center">
          <template #default="{ row }">
            <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'onTimeDeliveryCountDetails')">
              {{ row.onTimeDeliveryCount }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="delayCount" label="延误数数" width="100" align="center">
          <template #default="{ row }">
            <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'delayCountDetails')">
              {{ row.delayCount }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="achievementRate" label="达成率" width="100" align="center">
          <template #default="{ row }">
            <span>
              {{ row.achievementRate }}
            </span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next"
          :total="statisData.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          class="shadow-sm"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog v-model="detailVisible" title="详细信息" width="90%" append-to-body>
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-lg font-semibold">详细信息 (共{{ detailData.length }}条记录)</span>
          <el-button type="primary" :loading="detailExportLoading" @click="handleDetailExport">
            <el-icon><Download /></el-icon>
            下载Excel
          </el-button>
        </div>
      </template>

      <el-table :data="paginatedDetailData" border stripe>
        <el-table-column prop="moState" label="状态" width="100" align="center" />
        <el-table-column prop="remark" label="备注" width="150" show-overflow-tooltip />
        <el-table-column prop="customerCode" label="客户代码" width="120" align="center" />
        <el-table-column prop="pmcDeliveryDate" label="PMC要求交期" width="150" align="center" />
        <el-table-column prop="moNumber" label="MO号" width="140" align="center" />
        <el-table-column prop="delayDays" label="延误天数" width="100" align="center" />
        <el-table-column prop="drawNumber" label="图号" width="180" />
        <el-table-column prop="orderQty" label="订单数量" width="100" align="center" />
        <el-table-column prop="supplier" label="供应商" width="120" />
        <el-table-column prop="receiveInspectionTime" label="收货送检时间" width="150" align="center" />
        <el-table-column prop="lastProcessStartTime" label="末工序开始时间" width="150" align="center" />
        <el-table-column prop="outPrincipal" label="外发负责人" width="120" />
        <el-table-column prop="pmcPrincipal" label="PMC负责人" width="120" />
        <el-table-column prop="prType" label="PR类型" width="100" align="center" />
        <el-table-column prop="prNumber" label="PR编号" width="140" />
        <el-table-column prop="itemNumber" label="项次" width="80" align="center" />
        <el-table-column prop="isSupply" label="是否供料" width="100" align="center" />
        <el-table-column prop="documentReply" label="对单回复" width="150" show-overflow-tooltip />
      </el-table>

      <!-- 详情对话框分页 -->
      <div class="mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="detailCurrentPage"
          v-model:page-size="detailPageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next"
          :total="detailData.length"
          @size-change="handleDetailSizeChange"
          @current-change="handleDetailCurrentChange"
          class="shadow-sm"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, reactive, ref } from 'vue';
import {
  ElButton,
  ElCard,
  ElCheckbox,
  ElDatePicker,
  ElDialog,
  ElForm,
  ElFormItem,
  ElIcon,
  ElMessage,
  ElPagination,
  ElTable,
  ElTableColumn
} from 'element-plus';
import { Download, Refresh, Search } from '@element-plus/icons-vue';
import { getStatis8Data } from '@/api/pmc/statis8';
import { exportToExcel } from '@/utils/excel';

// 响应式表单数据
const searchForm = reactive({
  deliveryDateRange: null as [string, string] | null
});

// 统计数据
const statisData = ref([]);

// 分页相关变量
const currentPage = ref(1);
const pageSize = ref(10);

// 详情对话框分页相关变量
const detailCurrentPage = ref(1);
const detailPageSize = ref(10);

// 计算分页后的统计数据
const paginatedStatisData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return statisData.value.slice(start, end);
});

// 计算详情对话框分页后的数据
const paginatedDetailData = computed(() => {
  const start = (detailCurrentPage.value - 1) * detailPageSize.value;
  const end = start + detailPageSize.value;
  return detailData.value.slice(start, end);
});

// 加载状态
const loading = ref(false);
const exportLoading = ref(false);

// 详情对话框相关
const detailVisible = ref(false);
const detailExportLoading = ref(false);
const detailData = ref([]);
const currentDetailType = ref(''); // 记录当前详情类型，用于生成文件名

// 计算导出按钮是否禁用
const isExportDisabled = computed(() => {
  return statisData.value.length === 0;
});

// 处理页码变化
const handleCurrentChange = (val: number) => {
  currentPage.value = val;
};

// 处理每页条数变化
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  currentPage.value = 1;
};

// 处理详情对话框页码变化
const handleDetailCurrentChange = (val: number) => {
  detailCurrentPage.value = val;
};

// 处理详情对话框每页条数变化
const handleDetailSizeChange = (val: number) => {
  detailPageSize.value = val;
  detailCurrentPage.value = 1;
};

// 显示详情
const showDetail = (row: any, detailType: string) => {
  // 根据点击的列显示对应的详情数据
  detailData.value = row[detailType] || [];
  currentDetailType.value = detailType;
  // 重置详情对话框分页状态
  detailCurrentPage.value = 1;
  detailPageSize.value = 10;
  detailVisible.value = true;
};

// 查询方法
const handleQuery = async () => {
  loading.value = true;
  try {
    const res = await getStatis8Data(searchForm);
    if (res.data) {
      statisData.value = res.data;
    }

    ElMessage.success('查询成功');
  } catch (err: any) {
    ElMessage.error('查询失败：' + (err.message || '未知错误'));
  } finally {
    loading.value = false;
  }
};

// 重置方法
const handleReset = () => {
  searchForm.deliveryDateRange = null;
  currentPage.value = 1;
};

// 导出方法
const handleExport = async () => {
  exportLoading.value = true;
  try {
    if (statisData.value.length === 0) {
      ElMessage.warning('没有数据可以导出');
      return;
    }

    const exportData = statisData.value.map((item) => ({
      'ID': item.id,
      'PMC要求交期': item.pmcDeliveryDate,
      '应交货数数': item.expectedDeliveryCount,
      '已交货数数': item.actualDeliveryCount,
      '准时交货数数': item.onTimeDeliveryCount,
      '延误数数': item.delayCount,
      '达成率': item.achievementRate
    }));

    const headers = {
      'ID': 'ID',
      'PMC要求交期': 'PMC要求交期',
      '应交货数数': '应交货数数',
      '已交货数数': '已交货数数',
      '准时交货数数': '准时交货数数',
      '延误数数': '延误数数',
      '达成率': '达成率'
    };

    const fileName = `全工序外发交货达成率统计_${new Date().getTime()}`;
    await exportToExcel(exportData, headers, fileName, '统计信息', '409EFF');
    ElMessage.success('导出成功');
  } catch (err: any) {
    ElMessage.error('导出失败：' + err.message);
  } finally {
    exportLoading.value = false;
  }
};

// 详情导出方法
const handleDetailExport = async () => {
  if (!detailData.value || detailData.value.length === 0) {
    ElMessage.warning('没有数据可以导出');
    return;
  }

  detailExportLoading.value = true;
  try {
    // 定义表头映射
    const headers = {
      moState: '状态',
      remark: '备注',
      customerCode: '客户代码',
      pmcDeliveryDate: 'PMC要求交期',
      moNumber: 'MO号',
      delayDays: '延误天数',
      drawNumber: '图号',
      orderQty: '订单数量',
      supplier: '供应商',
      receiveInspectionTime: '收货送检时间',
      lastProcessStartTime: '末工序开始时间',
      outPrincipal: '外发负责人',
      pmcPrincipal: 'PMC负责人',
      prType: 'PR类型',
      prNumber: 'PR编号',
      itemNumber: '项次',
      isSupply: '是否供料',
      documentReply: '对单回复'
    };

    // 生成文件名
    const typeNameMap: Record<string, string> = {
      expectedDeliveryCountDetails: '应交货数数明细',
      actualDeliveryCountDetails: '已交货数数明细',
      onTimeDeliveryCountDetails: '准时交货数数明细',
      delayCountDetails: '延误数数明细'
    };

    const typeName = typeNameMap[currentDetailType.value] || '详情数据';
    const fileName = `全工序外发交货达成率统计_${typeName}_${new Date().getTime()}`;

    // 使用蓝色表头
    await exportToExcel(detailData.value, headers, fileName, '详情数据', '4472C4');
    ElMessage.success('导出成功');
  } catch (err: any) {
    ElMessage.error('导出失败：' + err.message);
  } finally {
    detailExportLoading.value = false;
  }
};

onMounted(() => {
});
</script>

<style scoped>
</style>
