<template>
  <div class="single-process-statistics">
    <!-- 查询条件 -->
    <el-form :model="queryForm" label-width="100px" size="small" inline>
      <el-form-item label="厂商简称">
        <el-input v-model="queryForm.vendorName" placeholder="请输入厂商简称" />
      </el-form-item>
      <el-form-item label="MO单号">
        <el-input v-model="queryForm.moNo" placeholder="请输入MO单号" />
      </el-form-item>
      <el-form-item label="客户代码">
        <el-input v-model="queryForm.customerCode" placeholder="请输入客户代码" />
      </el-form-item>
      <el-form-item label="客户类别">
        <el-select v-model="queryForm.customerType" placeholder="请选择客户类别" :disabled="true">
          <el-option label="自动化" value="自动化" />
          <el-option label="非自动化" value="非自动化" />
        </el-select>
      </el-form-item>
      <el-form-item label="PMC负责人">
        <el-input v-model="queryForm.pmcManager" placeholder="请输入PMC负责人" />
      </el-form-item>
      <el-form-item label="供应商">
        <el-input v-model="queryForm.supplierStatus" placeholder="请输入供应商" />
      </el-form-item>
      <el-form-item label="关闭否">
        <el-select v-model="queryForm.closed" placeholder="请选择">
          <el-option label="是" value="Y" />
          <el-option label="否" value="N" />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="工序类别">
        <el-select v-model="queryForm.processType" placeholder="请选择工序类别">
          <el-option label="单工序" value="Z316" />
          <el-option label="多工序" value="Z317" />
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button @click="resetQuery">清除查询条件</el-button>
      </el-form-item>
    </el-form>

    <!-- 统计信息表格 -->
      <el-table
        :data="statisticData"
        border
        height="500"
        v-loading="loading"
        @row-dblclick="row => dblclickSearch(row)"
        @selection-change="handleRowSelect"
      >
        <el-table-column type="selection" width="60" label="选择"></el-table-column>
        <el-table-column type="index" width="60" label="ID"></el-table-column>
        <el-table-column prop="profitCenter" label="利润中心" width="120"></el-table-column>
        <el-table-column prop="pmcManager" label="PMC跟单员" width="120"></el-table-column>
        <el-table-column prop="drawingCount" label="图纸统计" width="100"></el-table-column>
        <el-table-column prop="onTimeCount" label="交期达成" width="100"></el-table-column>
        <el-table-column prop="delayCount" label="交期延误" width="100"></el-table-column>
        <el-table-column prop="qualityReturnCount" label="品质退货数" width="120"></el-table-column>
        <el-table-column prop="achievementRate" label="达成率" width="100"></el-table-column>
        <el-table-column prop="actualAchievementRate" label="实际达成率" width="100"></el-table-column>
      </el-table>


    <!-- 明细弹窗 -->
      <el-dialog
        v-model="detailDialogVisible"
        title="外发记录明细"
        width="1200px"
        :close-on-click-modal="false"
      >
        <el-table
          :data="paginatedDetailData"
          border
          height="500"
          @selection-change="handleDetailRowSelect"
        >
          <el-table-column type="selection" width="60"></el-table-column>
          <el-table-column type="index" width="60" label="ID"></el-table-column>
          <el-table-column prop="moStatus" label="MO状态" width="120"></el-table-column>
          <el-table-column prop="vendorShortName" label="供应商简称" width="120"></el-table-column>
          <el-table-column prop="moNo" label="MO单号" width="150"></el-table-column>
          <el-table-column prop="drawingNo" label="图纸" width="180"></el-table-column>
          <el-table-column prop="customerCode" label="客户代码" width="120"></el-table-column>
          <el-table-column prop="prQuantity" label="PR数量" width="100"></el-table-column>
          <el-table-column prop="poQuantity" label="PO数量" width="100"></el-table-column>
          <el-table-column prop="poShortage" label="PO欠数" width="100"></el-table-column>
          <el-table-column prop="outsourceDate" label="外发时间" width="150"></el-table-column>
          <el-table-column prop="pmcRequestDate" label="PMC要求交期" width="150"></el-table-column>
          <el-table-column prop="pmcManager" label="PMC跟单员" width="120"></el-table-column>
          <el-table-column prop="outsourceManager" label="外发跟单员" width="120"></el-table-column>
          <el-table-column prop="uncompletedTime" label="未完时间" width="150"></el-table-column>
          <el-table-column prop="customerDueDate" label="客户要求交期" width="150"></el-table-column>
          <el-table-column prop="materialStatus" label="是否已出PO" width="120"></el-table-column>
          <el-table-column prop="poNo" label="PO单号" width="120"></el-table-column>
          <el-table-column prop="poItem" label="PO项次" width="120"></el-table-column>
          <el-table-column prop="poReview" label="PO审核" width="120"></el-table-column>
          <el-table-column prop="poCreateDate" label="PO创建日期" width="150"></el-table-column>
          <el-table-column prop="profitCenter" label="利润中心" width="120"></el-table-column>
        </el-table>
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="detailData.length"
          layout="prev, pager, next"
          @current-change="handlePageChange"
        ></el-pagination>
        <div class="dialog-footer">
          <el-button type="primary" @click="downloadSelectedDrawings">下载选中图纸</el-button>
        </div>
      </el-dialog>

    <!-- 底部操作栏 -->
    <div class="footer-bar">
      <span>总共 {{ totalRecords }} 条记录</span>
      <span style="margin-left: 20px;">共查找到 {{ detailTotalRecords }} 条外发记录！</span>
      <el-button type="primary" @click="showStatisticInfo">统计信息</el-button>
      <el-button @click="handleSearch">查 找</el-button>
      <!-- <el-button @click="downloadDrawings">图纸下载</el-button> -->
      <!-- <el-button @click="goBack">返 回</el-button> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { getEntireProcessStatistics ,downloadDraw} from '@/api/pmc/processStatistics';

// 查询表单数据
const queryForm = reactive({
  vendorName: '',
  moNo: '',
  customerCode: '',
  customerType: '自动化',
  pmcManager: '',
  supplierStatus: '',
  closed: 'N',
  processType: 'Z315'
})

// 统计数据
const statisticData = ref([])

// 明细数据
const detailData = ref([])

// 明细数据
const detailDataStore = ref([])

// 弹窗控制
const detailDialogVisible = ref(false)

// 需要先在组件顶部声明loading变量
const loading = ref(false);

// 总记录数
const totalRecords = ref(0)

// 明细总记录数
const detailTotalRecords = ref(0)

const currentPage = ref(1);
const pageSize = ref(10);
const paginatedDetailData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return detailData.value.slice(start, end);
});

const handlePageChange = (page) => {
  currentPage.value = page;
};
// 搜索处理
const fetchData = async () => {
  try {
    const { data: { rows, total,details,detailTotal } } = await getEntireProcessStatistics(queryForm);
    statisticData.value = rows;
    totalRecords.value = total;
    detailData.value = details;
    detailTotalRecords.value = detailTotal;
    detailDataStore.value = details;
  } catch (error) {
    ElMessage.error('获取数据失败');
  }
};

const handleSearch = () => {
    const selectedRows = statisticData.value.filter(row => row.selected);
    if (selectedRows.length === 0) {
        ElMessage.warning('请先勾选至少一行');
        return;
    }

    const matchingDetails = detailDataStore.value.filter(detail =>
        selectedRows.some(selectedRow =>
            ((detail as any).profitCenter || null) === ((selectedRow as any)?.profitCenter || null) &&
            ((detail as any).pmcManager || null) === ((selectedRow as any)?.pmcManager || null)
        )
    );

    if (matchingDetails.length > 0) {
        detailData.value = matchingDetails;
        detailDialogVisible.value = true; // Trigger modal
    } else {
        console.warn('No matching details found');
    }
};


const dblclickSearch = async (row) => {
    console.log('dblclickSearch', row);
    const selectedRow = row;
    if (!selectedRow) {
        ElMessage.warning('请先选择一行');
        return;
    }

    const matchingDetails = detailDataStore.value.filter(detail =>
         ((detail as any).profitCenter || null) === ((selectedRow as any)?.profitCenter || null) &&
            ((detail as any).pmcManager || null) === ((selectedRow as any)?.pmcManager || null)
    );

    if (matchingDetails.length > 0) {
        detailData.value = [...matchingDetails]; // 防止直接引用导致逻辑错误
        detailDialogVisible.value = true; // Trigger modal
    } else {
        console.warn('No matching details found');
    }
};

// 清除查询条件
const resetQuery = () => {
  Object.keys(queryForm).forEach(key => {
    if (!['customerType', 'processType', 'closed'].includes(key)) {
      queryForm[key as keyof typeof queryForm] = ''
    }
  })
}

// 显示统计信息
const showStatisticInfo = () => {
  loading.value = true;
  // if (!queryForm.moNo && !queryForm.pmcManager) {
  //   ElMessage.warning('请至少输入MO单号或PMC负责人');
  //   loading.value = false;
  //   return;
  // }
  fetchData()
    .then(() => {
      loading.value = false; 
    })
    .catch(error => {
      ElMessage.error('获取数据失败');
      loading.value = false; 
    });
};


const handleRowSelect = (selectedRows) => {
  statisticData.value.forEach(row => {
    row.selected = selectedRows.includes(row);
  });
};
const selectedRows = ref([]);
const handleDetailRowSelect = (rows) => {
  selectedRows.value = rows;
};

const downloadSelectedDrawings = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先勾选一行');
    return;
  }
  if (selectedRows.value.length > 1) {
    ElMessage.warning('只能勾选一行');
    return;
  }
  const selectedRow = selectedRows.value[0];
  downloadDraw({ moNo: selectedRow.moNo, drawingNo: selectedRow.drawingNo })
    .then((response) => {
      const link = document.createElement('a');
      link.href = response.data;
      link.download = `${selectedRow.drawingNo}.pdf`;
      link.click();
    })
    .catch(() => {
      ElMessage.error('下载失败');
    });
};
</script>

<style scoped>
.single-process-statistics {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.statistic-table {
  margin-top: 20px;
  background: white;
  padding: 15px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.footer-bar {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-top: 1px solid #ddd;
}

.el-form {
  margin-bottom: 20px;
}
</style>