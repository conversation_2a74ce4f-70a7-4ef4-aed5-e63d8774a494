<template>
  <div v-loading="loading" element-loading-text="加载中...">
    <el-card>
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" @keyup.enter="getDataList()">
        <div>
          <el-form-item prop="bargainDate" label="接单日期" label-width="100">
            <el-date-picker style="width: 250px" clearable v-model="queryParams.bargainDate" type="daterange"
              value-format="YYYY-MM-DD" start-placeholder="接单日期(开始)" end-placeholder="接单日期(结束)" />
          </el-form-item>
          <el-form-item prop="customerCode">
            <el-input placeholder="客户代码" :style="widthStyle" v-model="queryParams.customerCode" clearable />
          </el-form-item>
          <el-form-item prop="moNumber">
            <el-input :style="widthStyle" v-model="queryParams.moNumber" placeholder="DR单号" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="getDataList">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            <el-button :icon="Download" @click="handleExport">
              下载
            </el-button>
          </el-form-item>
        </div>
        <div>
          <el-form-item prop="pmcReqDate" label="PMC要求交期" label-width="100">
            <el-date-picker style="width: 250px" clearable v-model="queryParams.pmcReqDate" type="daterange"
              value-format="YYYY-MM-DD" start-placeholder="日期(开始)" end-placeholder="日期(结束)" />
          </el-form-item>
          <el-form-item prop="merchandiser">
            <el-select :style="widthStyle" v-model="queryParams.merchandiser" placeholder="跟单工程师">
              <el-option v-for="item in merchandisers" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item prop="status">
            <el-select :style="widthStyle" v-model="queryParams.status" placeholder="完成状态">
              <el-option label="完成" value="all" />
              <el-option label="未完成" value="unfinished" />
            </el-select>
          </el-form-item>
        </div>

      </el-form>

      <el-divider style="margin-top: 6px;" />

      <el-table border :data="dataList" show-overflow-tooltip show-summary :summary-method="getSummaries">
        <el-table-column label="跟单人" align="center" prop="merchandiser" />
        <el-table-column label="特急件图纸数" :width="widthMedium" align="center" prop="totalPapers">
          <template #default="scope">
            <el-text @click="openDetailDialogHandle(scope.row.totalPaperDetails)" class="hover" type="primary">{{
              scope.row.totalPapers
            }}</el-text>
          </template>
        </el-table-column>
        <el-table-column label="交期达成" align="center" prop="finishedTotalPapers">
          <template #default="scope">
            <el-text @click="openDetailDialogHandle(scope.row.finishedTotalPaperDetails)" class="hover"
              type="primary">{{
                scope.row.finishedTotalPapers
              }}</el-text>
          </template>
        </el-table-column>
        <el-table-column label="延误项数" align="center" prop="delayTotalPapers">
          <template #default="scope">
            <el-text @click="openDetailDialogHandle(scope.row.delayTotalPaperDetails)" class="hover" type="primary">{{
              scope.row.delayTotalPapers
            }}</el-text>
          </template>
        </el-table-column>
        <el-table-column label="交货达成率" :formatter="numberFormatter" :width="widthNarrow" align="center"
          prop="totalRate" />

        <el-table-column label="生产" align="center" prop="totalProductionPapers">
          <template #default="scope">
            <el-text @click="openDetailDialogHandle(scope.row.totalProductionPaperDetails)" class="hover"
              type="primary">{{
                scope.row.totalProductionPapers
              }}</el-text>
          </template>
        </el-table-column>
        <el-table-column label="生产延误项数" :width="widthMedium" align="center" prop="delayProductionPapers">
          <template #default="scope">
            <el-text @click="openDetailDialogHandle(scope.row.delayProductionPaperDetails)" class="hover"
              type="primary">{{
                scope.row.delayProductionPapers
              }}</el-text>
          </template>
        </el-table-column>
        <el-table-column label="生产达成率" :width="widthNarrow" :formatter="numberFormatter" align="center"
          prop="productionRate" />

        <el-table-column label="钣金" align="center" prop="totalMetalPlatePapers">
          <template #default="scope">
            <el-text @click="openDetailDialogHandle(scope.row.totalMetalPlatePaperDetails)" class="hover"
              type="primary">{{
                scope.row.totalMetalPlatePapers
              }}</el-text>
          </template>
        </el-table-column>
        <el-table-column label="钣金延误项数" :width="widthMedium" align="center" prop="delayMetalPlatePapers">
          <template #default="scope">
            <el-text @click="openDetailDialogHandle(scope.row.delayMetalPlatePaperDetails)" class="hover"
              type="primary">{{
                scope.row.delayMetalPlatePapers
              }}</el-text>
          </template>
        </el-table-column>
        <el-table-column label="钣金达成率" :width="widthNarrow" :formatter="numberFormatter" align="center"
          prop="metalPlateRate" />

        <el-table-column label="外发" align="center" prop="totalDeliveryPapers">
          <template #default="scope">
            <el-text @click="openDetailDialogHandle(scope.row.totalDeliveryPaperDetails)" class="hover"
              type="primary">{{
                scope.row.totalDeliveryPapers
              }}</el-text>
          </template>
        </el-table-column>
        <el-table-column label="外发延误项数" :width="widthMedium" align="center" prop="delayDeliveryPapers">
          <template #default="scope">
            <el-text @click="openDetailDialogHandle(scope.row.delayDeliveryPaperDetails)" class="hover"
              type="primary">{{
                scope.row.delayDeliveryPapers
              }}</el-text>
          </template>
        </el-table-column>
        <el-table-column label="外发达成率" :width="widthNarrow" :formatter="numberFormatter" align="center"
          prop="deliveryRate" />

        <el-table-column label="QA" align="center" prop="totalQAPapers">
          <template #default="scope">
            <el-text @click="openDetailDialogHandle(scope.row.totalQAPaperDetails)" class="hover" type="primary">{{
              scope.row.totalQAPapers
              }}</el-text>
          </template>
        </el-table-column>
        <el-table-column label="QA延误项数" :width="widthMedium" align="center" prop="delayQAPapers">
          <template #default="scope">
            <el-text @click="openDetailDialogHandle(scope.row.delayQAPaperDetails)" class="hover" type="primary">{{
              scope.row.delayQAPapers
              }}</el-text>
          </template>
        </el-table-column>
        <el-table-column label="QA达成率" :width="widthNarrow" :formatter="numberFormatter" align="center" prop="qarate" />

        <el-table-column label="生产交货" align="center" prop="finishedProductionPapers" />
        <el-table-column label="钣金交货" align="center" prop="finishedMetalPlatePapers" />
        <el-table-column label="外发交货" align="center" prop="finishedDeliveryPapers" />
        <el-table-column label="QA交货" align="center" prop="finishedQAPapers" />
      </el-table>
    </el-card>

    <mo-detail table-name="特急件跟进表详情" v-model="dialogVisible" :data-list="moDetailList"
      download-url="/pmc/very-urgent-follow/details/download" />
  </div>
</template>

<script setup name="VeryUrgentFollow" lang="ts">
import MoDetail from '@/components/UrgentDetailDialog/index.vue';
import { getStatistics, getMerchandisers, getMoDetails } from '@/api/pmc/veryUrgentFollow/api';
import dayjs from 'dayjs';
import { Download } from '@element-plus/icons-vue'
const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const dataList = ref<any[]>([]);
const moDetailList = ref<any[]>([]);
const dialogVisible = ref(false);
const loading = ref(false);
const merchandisers = ref<any[]>([]);
const widthNarrow = 100
const widthMedium = 150
const widthWide = 200
const widthStyle = ref({
  'width': widthWide + 'px'
})
const minWidthStyle = ref({
  'minWidth': widthWide + 'px'
})
const queryFormRef = ref();
const queryParams = reactive({
  bargainDate: [
    dayjs().startOf('day').subtract(7, 'day').format('YYYY-MM-DD'),
    dayjs().endOf('day').format('YYYY-MM-DD')
  ],
  pmcReqDate: '',
  merchandiser: '',
  moNumber: '',
  customerCode: '',
  status: 'all'
});
const summaryItem = ref([] as any[]);

const getQueryParams = (): Object => {
  let query = JSON.parse(JSON.stringify(queryParams));
  query.bargainDate[0] += ' 00:00:00'
  query.bargainDate[1] += ' 23:59:59'
  return query;
}

const getDataList = async () => {
  loading.value = true;
  getStatistics(getQueryParams())
    .then((res) => {
      // let _summaryItem = [];
      let totalItem = res.data.pop();
      summaryItem.value = [
        totalItem.merchandiser,
        totalItem.totalPapers,
        totalItem.finishedTotalPapers,
        totalItem.delayTotalPapers,
        numberFormatter(null, null, totalItem.totalRate),
        totalItem.totalProductionPapers,
        totalItem.delayProductionPapers,
        null,
        totalItem.totalMetalPlatePapers,
        totalItem.delayMetalPlatePapers,
        null,
        totalItem.totalDeliveryPapers,
        totalItem.delayDeliveryPapers,
        null,
        totalItem.totalQAPapers,
        totalItem.delayQAPapers,
        null,
        null,
        null,
        null,
        null,
      ];
      dataList.value = res.data;
    })
    .finally(() => {
      loading.value = false;
    });
}

const openDetailDialogHandle = (moList: string[]) => {
  loading.value = true;
  getMoDetails(moList).then((res) => {
    dialogVisible.value = true;
    moDetailList.value = res.data;
  }).finally(() => {
    loading.value = false;
  });
}

const getSummaries = (param: any) => {
  return summaryItem.value;
}

/** 获取跟单工程师列表 */
const getMerchandiserList = async () => {
  getMerchandisers().then((res) => {
    merchandisers.value = res.data.map((item: any) => {
      return {
        label: item,
        value: item
      }
    });
  });
}

const numberFormatter = (row: any, column: any, cellValue: any) => {
  if (cellValue === null || typeof cellValue === 'undefined' || cellValue === '') {
    return ''
  }
  return (Math.round(cellValue * 100) / 100).toString() + '%';
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  getDataList();
}

/** 导出按钮操作 */
const handleExport = () => {
  try {
    proxy?.download('/pmc/very-urgent-follow/statistics/download', {
      ...getQueryParams()
    }, `特急件跟进表${new Date().getTime()}.xlsx`)
  } catch (error) {
    ElMessage.error('导出失败');
  }
}

onMounted(() => {
  getDataList();
  getMerchandiserList();
});

</script>

<style lang="scss" scoped>
.el-form-item {
  margin-right: 16px;
}

.hover {
  font-weight: bold;

  &:hover {
    cursor: pointer;
    text-decoration: underline;
  }
}
</style>