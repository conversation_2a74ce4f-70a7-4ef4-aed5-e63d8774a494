<template>
  <div class="p-2" v-loading="loading" element-loading-text="加载中...">
    <el-card>
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" @keyup.enter="getDataList()">
        <el-form-item prop="bargainDate" label="接单日期">
          <el-date-picker style="width: 250px" clearable v-model="queryParams.bargainDate" type="daterange"
            value-format="YYYY-MM-DD" start-placeholder="接单日期(开始)" end-placeholder="接单日期(结束)" />
        </el-form-item>
        <el-form-item prop="merchandiser">
          <el-select :style="widthStyle" v-model="queryParams.merchandiser" placeholder="跟单工程师">
            <el-option v-for="item in merchandisers" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item prop="drNumber">
          <el-input :style="widthStyle" v-model="queryParams.drNumber" placeholder="DR单号" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getDataList">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          <el-button :icon="Download" @click="handleExport">
            下载
          </el-button>
        </el-form-item>
      </el-form>

      <el-divider />

      <el-table border :data="dataList" show-overflow-tooltip show-summary :summary-method="getSummaries">
        <el-table-column label="备注" :width="widthWide" align="center" prop="moRem" />
        <el-table-column label="MO号" :width="widthMedium" align="center" prop="moNo" />
        <el-table-column label="利润中心" align="center" prop="profitCenter" />
        <el-table-column label="项目号" align="center" prop="pmNo" />
        <el-table-column label="分组时间" :width="widthNarrow" align="center" prop="groupTime" />
        <el-table-column label="pmc要求交期" :width="widthMedium" align="center" prop="pmcReqDate" />
        <el-table-column label="DR号" :width="widthNarrow" align="center" prop="drNo" />
        <el-table-column label="下单员" :width="widthMedium" align="center" prop="orderSalm" />
        <el-table-column label="跟单人" align="center" prop="merchandiser" />
        <el-table-column label="品号" :width="widthMedium" align="center" prop="partId" />
        <el-table-column label="图号" :width="widthWide + 100" align="center" prop="drawNumber" />
        <el-table-column label="生产数量" align="center" prop="productionQty" />
        <el-table-column label="计划类型" :width="widthMedium" align="center" prop="planType" />

        <el-table-column label="CNC" align="center" prop="cnc" :formatter="numberFormatter" />
        <el-table-column label="铣" align="center" prop="milling" :formatter="numberFormatter" />
        <el-table-column label="车" align="center" prop="turning" :formatter="numberFormatter" />
        <el-table-column label="NC" align="center" prop="nc" :formatter="numberFormatter" />
        <el-table-column label="平磨" align="center" prop="surfaceGrinding" :formatter="numberFormatter" />
        <el-table-column label="快" align="center" prop="fastWireEdm" :formatter="numberFormatter" />
        <el-table-column label="慢" align="center" prop="slowWireEdm" :formatter="numberFormatter" />
        <el-table-column label="钣金" align="center" prop="sheetMetal" :formatter="numberFormatter" />
        <el-table-column label="披锋" align="center" prop="burrRemoval" :formatter="numberFormatter" />
        <el-table-column label="钳" align="center" prop="fitting" :formatter="numberFormatter" />
        <el-table-column label="EDM" align="center" prop="edm" :formatter="numberFormatter" />
        <el-table-column label="是否包胶" align="center" prop="isRubberCoated" :formatter="numberFormatter" />
        <el-table-column label="是否电镀" align="center" prop="isPlated" :formatter="numberFormatter" />
        <el-table-column label="是否热处理" :width="widthMedium" align="center" prop="isHeatTreated"
          :formatter="numberFormatter" />
        <el-table-column label="是否喷涂" align="center" prop="isSprayCoated" :formatter="numberFormatter" />
        <el-table-column label="镀陶瓷" align="center" prop="isCeramicCoating" :formatter="numberFormatter" />
        <el-table-column label="镀铁氟龙" align="center" prop="isTeflonCoating" :formatter="numberFormatter" />
        <el-table-column label="总工时" align="center" prop="sum" :formatter="numberFormatter" />
      </el-table>

      <pagination :hide-on-single-page="true" :total="queryParams.total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" :autoScroll="false" @pagination="getDataList"
        @current-change="pageChange" />
    </el-card>
  </div>
</template>

<script setup name="VeryUrgent" lang="ts">
import { pageDataList, getMerchandisers } from '@/api/pmc/veryUrgent/api';
import dayjs from 'dayjs';
import { Download } from '@element-plus/icons-vue'
const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const dataList = ref<any[]>([]);
const loading = ref(false);
// const downloadLoading = ref(false);
const merchandisers = ref<any[]>([]);
const widthNarrow = 100
const widthMedium = 150
const widthWide = 200
const widthStyle = ref({
  'width': widthWide + 'px'
})
const minWidthStyle = ref({
  'minWidth': widthWide + 'px'
})
const queryFormRef = ref();
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0,
  bargainDate: [
    dayjs().startOf('day').subtract(7, 'day').format('YYYY-MM-DD'),
    dayjs().endOf('day').format('YYYY-MM-DD')
  ],
  merchandiser: '',
  drNumber: '',
});
const summaryItem = ref([] as any[]);

const getQueryParams = (): Object => {
  let query = JSON.parse(JSON.stringify(queryParams));
  query.bargainDate[0] += ' 00:00:00'
  query.bargainDate[1] += ' 23:59:59'
  return query;
}

const getDataList = async () => {
  loading.value = true;
  pageDataList(getQueryParams())
    .then((res) => {
      // remove the last row
      let totalItem = res.data.rows.pop()
      let _summaryItem = ['合计'];
      // 12 为不统计的列数
      for (let i = 0; i < 12; i++) {
        _summaryItem.push('')
      }
      let properties = ['cnc', 'milling', 'turning', 'nc', 'surfaceGrinding', 'fastWireEdm', 'slowWireEdm', 'sheetMetal', 'burrRemoval', 'fitting', 'edm']
      for (const field of properties) {
        _summaryItem.push(totalItem[field])
      }
      for (let i = 0; i < 6; i++) {
        _summaryItem.push('')
      }
      _summaryItem.push(totalItem['sum'])
      summaryItem.value = _summaryItem;
      dataList.value = res.data.rows;
      queryParams.total = res.data.total - 1;
    })
    .finally(() => {
      loading.value = false;
    });
}

const getSummaries = (param: any) => {
  return summaryItem.value;
}

/** 获取跟单工程师列表 */
const getMerchandiserList = async () => {
  getMerchandisers().then((res) => {
    merchandisers.value = res.data.map((item: any) => {
      return {
        label: item,
        value: item
      }
    });
  });
}

const numberFormatter = (row: any, column: any, cellValue: any) => {
  if (cellValue === null || typeof cellValue === 'undefined' || cellValue === '') {
    return ''
  }
  if (typeof cellValue === 'boolean') {
    return cellValue ? 'Y' : '';
  }
  return (Math.round(cellValue * 100) / 100).toString();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  getDataList();
}

/** 导出按钮操作 */
const handleExport = () => {
  try {
    proxy?.download('/pmc/very-urgent/man-hour-statistics/download', {
      ...getQueryParams()
    }, `特急件工时统计表${new Date().getTime()}.xlsx`)
  } catch (error) {
    ElMessage.error('导出失败');
  }
}

onMounted(() => {
  getDataList();
  getMerchandiserList();
});

const pageChange = (val: number) => {
  queryParams.pageNum = val;
  getDataList();
}

</script>

<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 0;
  margin-right: 16px;
}
</style>