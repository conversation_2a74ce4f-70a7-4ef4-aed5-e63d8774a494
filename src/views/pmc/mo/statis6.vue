<template>
  <div class="p-4">
    <!-- 搜索卡片 -->
    <el-card class="mb-6 shadow-md">
      <template #header>
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold">条件搜索</h3>
        </div>
      </template>

      <el-form :model="searchForm" label-width="100px" :inline="true">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="接单日期">
              <el-date-picker
                v-model="searchForm.bargainDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 240px"
              />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="客户代码">
              <el-input v-model="searchForm.customerCode" clearable style="width: 200px" />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="DR单号">
              <el-input v-model="searchForm.drNumber" clearable style="width: 200px" />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="负责人">
              <el-select v-model="searchForm.merchandiser" clearable :disabled="enable" style="width: 200px">
                <el-option v-for="item in mData" :key="item" :label="item" :value="item" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="客户要求交期">
              <el-date-picker
                v-model="searchForm.deliveryDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 240px"
              />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="利润中心">
              <el-select v-model="searchForm.profitCenter" clearable style="width: 200px">
                <el-option v-for="item in profitCenters" :key="item" :label="item" :value="item" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="MO单号">
              <el-input v-model="searchForm.moNumber" clearable style="width: 200px" />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="装配技师">
              <el-input v-model="searchForm.assemblyTechnician" clearable style="width: 200px" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="状态">
              <el-select v-model="searchForm.status" clearable style="width: 200px">
                <el-option label="已装配" value="已装配" />
                <el-option label="未装配" value="未装配" />
                <el-option label="全部" value="全部" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="结案">
              <el-select v-model="searchForm.isClosed" clearable style="width: 200px">
                <el-option label="Y" value="Y" />
                <el-option label="N" value="N" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24" class="flex justify-end">
            <el-button type="primary" :loading="loading" @click="handleQuery">
              <el-icon>
                <Search />
              </el-icon>
              查询
            </el-button>
            <el-button @click="handleReset">
              <el-icon>
                <Delete />
              </el-icon>
              重置
            </el-button>
            <el-button @click="handleModify">
              <el-icon>
                <Edit />
              </el-icon>
              修改
            </el-button>
            <el-button type="warning" :loading="exportLoading" @click="handleExport">
              <el-icon>
                <Download />
              </el-icon>
              下载
            </el-button>
            <el-button type="info" :loading="loading" @click="handleLoadNewData">
              <el-icon>
                <Refresh />
              </el-icon>
              加载新数据
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="shadow-md">
      <el-table ref="tableRef" :data="paginatedData" :loading="loading" border stripe @selection-change="handleSelectionChange" highlight-current-row @current-change="handleCurrentChange">
        <el-table-column width="55" align="center">
          <template #default="scope">
            <el-radio v-model="selectedRow" :label="scope.row.id" @change="handleRadioChange(scope.row)">&nbsp;</el-radio>
          </template>
        </el-table-column>
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="status" label="状态" width="120" align="center">
          <template #default="{ row }">
            <span @dblclick="viewScanRecords(row)" class="cursor-pointer">
              {{ row.status }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="moNo" label="mo_no" width="150" align="center">
          <template #default="{ row }">
            <span @dblclick="viewMaterialInfo(row)" class="cursor-pointer relative" :class="{ 'loading': materialInfoLoading }">
              {{ row.moNo }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="drItm" label="DR_ITM" width="100" align="center" />
        <el-table-column prop="drawNumber" label="图号" width="200" align="center">
          <template #default="{ row }">
            <span @dblclick="handleViewPic(row)" class="cursor-pointer">
              {{ row.drawNumber }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="custNo" label="客户代码" width="120" align="center" />
        <el-table-column prop="orderDate" label="接单日期" width="150" align="center" />
        <el-table-column prop="custPoNo" label="客户PO号" width="150" align="center" />
        <el-table-column prop="deliveryDate" label="客户要求交期" width="150" align="center" />
        <el-table-column prop="pmcReqDate" label="PMC要求交期" width="150" align="center" />
        <el-table-column prop="orderQty" label="订单数量" width="100" align="center" />
        <el-table-column prop="lastOpTime" label="末工序时间" width="150" align="center" />
        <el-table-column prop="assemblerExceptionRemark" label="装配异常备注" width="150" align="center">
          <template #default="{ row }">
            <span>{{ row.assemblerExceptionRemark }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="qaExceptionRemark" label="QA异常备注" width="150" align="center">
          <template #default="{ row }">
            <span>{{ row.qaExceptionRemark }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="pmcRemark" label="PMC备注" width="150" align="center">
          <template #default="{ row }">
            <span>{{ row.pmcRemark }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="ordererName" label="下单员姓名" width="120" align="center" />
        <el-table-column prop="merchandiser" label="跟单负责人" width="120" align="center" />
        <el-table-column prop="assembler" label="装配技师" width="120" align="center" />
        <el-table-column prop="bomCharge" label="研发工程师" width="120" align="center" />
        <el-table-column prop="pmcNotifyAssembly" label="PMC通知装配领料" width="150" align="center">
          <template #default="{ row }">
            <span>{{ row.pmcNotifyAssembly }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="assemblyStartTime" label="装配领料开工时间" width="150" align="center" />
        <el-table-column prop="partBomCount" label="零件BOM清单" width="120" align="center" />
        <el-table-column prop="standardBomCount" label="标准件BOM清单" width="120" align="center" />
        <el-table-column prop="totalProductionDrawings" label="生产图纸总数" width="120" align="center" />
        <el-table-column prop="uncompletedProduction" label="生产未完成图纸数" width="120" align="center" />
        <el-table-column prop="totalOutsourced" label="外发图纸总数" width="120" align="center" />
        <el-table-column prop="uncompletedOutsourced" label="外发图纸未完成数" width="120" align="center" />
        <el-table-column prop="totalSheetMetal" label="钣金图纸总数" width="120" align="center" />
        <el-table-column prop="uncompletedSheetMetal" label="钣金图纸未完成数" width="120" align="center" />
        <el-table-column prop="qaDrawingCount" label="QA图纸数" width="120" align="center" />
        <el-table-column prop="totalStandardParts" label="标准件总项数" width="120" align="center" />
        <el-table-column prop="uncompletedStandardParts" label="标准件未完成项数" width="120" align="center" />
        <el-table-column prop="soNo" label="SO_NO" width="120" align="center" />
        <el-table-column prop="soItm" label="SO_ITM" width="120" align="center" />
        <el-table-column prop="profitCenter" label="利润中心" width="120" align="center" />
        <el-table-column prop="partType" label="零件分类" width="120" align="center" />
        <el-table-column prop="planType" label="计划类型" width="120" align="center" />
        <el-table-column prop="status1" label="状态1" width="120" align="center" />
        <el-table-column prop="remark" label="备注" width="120" align="center" />
      </el-table>

      <!-- 分页 -->
      <div class="mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next"
          :total="totalRecords"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          class="shadow-sm"
        />
      </div>
    </el-card>

    <!-- 扫描记录对话框 -->
    <el-dialog v-model="scanRecordVisible" title="扫描记录" width="95%" append-to-body>
      <el-button type="success" :loading="scanExportLoading" @click="handleExportScanRecords" :disabled="scanRecords.length === 0" class="mb-4">
        <el-icon>
          <Download />
        </el-icon>
        导出扫描记录
      </el-button>
      <el-table :data="scanRecords" border stripe max-height="600">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="pjNumber" label="PJ号" width="120" />
        <el-table-column prop="processNumber" label="工序号" width="100" />
        <el-table-column prop="processName" label="工序名称" width="150" />
        <el-table-column prop="planType" label="计划类型" width="100" />
        <el-table-column prop="identifier" label="标识" width="120" />
        <el-table-column prop="operator" label="操作员" width="100" />
        <el-table-column prop="name" label="姓名" width="100" />
        <el-table-column prop="machineTool" label="机床" width="120" />
        <el-table-column prop="action" label="动作" width="120" />
        <el-table-column prop="startTime" label="开始时间" width="150" />
        <el-table-column prop="endTime" label="结束时间" width="150" />
        <el-table-column prop="passQuantity" label="通过数量" width="100" />
        <el-table-column prop="scrapQuantity" label="报废数量" width="100" />
        <el-table-column prop="totalProgress" label="总进度" width="100" />
        <el-table-column prop="currentProgress" label="当前进度" width="100" />
        <el-table-column prop="processHours" label="工艺工时" width="100" />
        <el-table-column prop="actualTime" label="实际时间" width="100" />
        <el-table-column prop="remark" label="备注" width="150" />
        <el-table-column prop="methodDescription" label="作法描述" width="150" />
        <el-table-column prop="supplier" label="供应商" width="120" />
        <el-table-column prop="deliveryNoteNumber" label="送货单号" width="120" />
        <el-table-column prop="deliveryTime" label="送货时间" width="150" />
        <el-table-column prop="inspectionNoteNumber" label="送检单号" width="120" />
        <el-table-column prop="receiptVoucher" label="收货凭证" width="120" />
        <el-table-column prop="batNo" label="BAT_NO" width="120" />
        <el-table-column prop="orderCategory" label="订单类别" width="100" />
        <el-table-column prop="customerCode" label="客户代码" width="120" />
        <el-table-column prop="furnaceNumber" label="炉号" width="120" />
        <el-table-column prop="processingPlant" label="加工厂址" width="150" />
        <el-table-column prop="postingDate" label="过账日期" width="150" />
        <el-table-column prop="movementType" label="移动类型" width="100" />
        <el-table-column prop="company" label="公司" width="120" />
        <el-table-column prop="factory" label="工厂" width="120" />
        <el-table-column prop="materialNumber" label="物料编号" width="120" />
        <el-table-column prop="materialDescription" label="物料描述" width="150" />
        <el-table-column prop="storageLocation" label="库存地点" width="120" />
        <el-table-column prop="purchasePoNumber" label="采购PO号" width="120" />
        <el-table-column prop="lineItem" label="行项目" width="100" />
        <el-table-column prop="sameBatchNumber" label="同批批号" width="120" />
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column prop="costCenter" label="成本中心" width="120" />
        <el-table-column prop="drawingNumber" label="图号" width="120" />
        <el-table-column prop="version" label="版本" width="80" />
        <el-table-column prop="excessQuantity" label="多余数量" width="100" />
        <el-table-column prop="industry" label="行业" width="100" />
        <el-table-column prop="fhid" label="FHID" width="120" />
        <el-table-column prop="deliveryNote" label="送货单" width="120" />
        <el-table-column prop="dnNumber" label="DN单号" width="120" />
        <el-table-column prop="dnLineNumber" label="DN行号" width="100" />
        <el-table-column prop="departmentCode" label="部门代号" width="100" />
        <el-table-column prop="department" label="部门" width="120" />
        <el-table-column prop="storagePosition" label="库位" width="120" />
        <el-table-column prop="materialRequester" label="领料人" width="100" />
        <el-table-column prop="materialRequesterName" label="领料姓名" width="100" />
        <el-table-column prop="factoryExitApplication" label="出厂申请" width="120" />
        <el-table-column prop="partClassification" label="零件分类" width="120" />
        <el-table-column prop="productNumber" label="品号" width="120" />
        <el-table-column prop="isCombinedOrder" label="是否合单" width="100" />
      </el-table>
    </el-dialog>

    <el-dialog v-model="editDialogVisible" title="修改数据" width="70%" :before-close="handleClose">
      <el-form :model="editForm" label-width="150px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="MO号">
              <el-input v-model="editForm.moNo" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="装配异常备注">
              <el-select v-model="editForm.assemblerExceptionRemark" style="width: 100%" clearable>
                <el-option label="工程设计变更" value="工程设计变更" />
                <el-option label="工程师修改零件" value="工程师修改零件" />
                <el-option label="零件/标准件漏下" value="零件/标准件漏下" />
                <el-option label="缺少刻字/铭牌信息" value="缺少刻字/铭牌信息" />
                <el-option label="零件品质问题" value="零件品质问题" />
                <el-option label="工程师调试" value="工程师调试" />
                <el-option label="订单积压" value="订单积压" />
                <el-option label="标准件退货" value="标准件退货" />
                <el-option label="其它" value="其它" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="QA异常备注">
              <el-select v-model="editForm.qaExceptionRemark" style="width: 100%" clearable>
                <el-option label="供应商未交齐少数" value="供应商未交齐少数" />
                <el-option label="其它" value="其它" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="PMC备注">
              <el-input v-model="editForm.pmcRemark" type="textarea" :rows="3" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="PMC通知装配领料">
              <el-select v-model="editForm.pmcNotifyAssembly" style="width: 100%" clearable>
                <el-option label="领料" value="领料" />
                <el-option label="齐料" value="齐料" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注">
              <el-input v-model="editForm.remark" type="textarea" :rows="3" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="flex justify-end">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmEdit">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 物料信息对话框 -->
    <el-dialog v-model="materialInfoVisible" title="物料信息" width="90%" append-to-body>
      <el-table :data="materialInfoList" border stripe max-height="600">
        <el-table-column prop="moNo" label="MO单号" min-width="120" />
        <el-table-column prop="itemNo" label="品号" min-width="120" />
        <el-table-column prop="itemSeq" label="项次" min-width="80" />
        <el-table-column prop="materialName" label="材料名称" min-width="120" />
        <el-table-column prop="materialSpec" label="材料规格" min-width="120" />
        <el-table-column prop="length" label="长度" min-width="80" />
        <el-table-column prop="width" label="宽度" min-width="80" />
        <el-table-column prop="height" label="高度" min-width="80" />
        <el-table-column prop="diameter" label="直径" min-width="80" />
        <el-table-column prop="innerDiameter" label="内径" min-width="80" />
        <el-table-column prop="density" label="比重" min-width="80" />
        <el-table-column prop="unit" label="单位" min-width="80" />
        <el-table-column prop="materialUnit" label="材料单位" min-width="80" />
        <el-table-column prop="densityUnit" label="比重单位" min-width="80" />
        <el-table-column prop="unitUsage" label="单位用量" min-width="80" />
        <el-table-column prop="componentQty" label="组件数量" min-width="80" />
        <el-table-column prop="lossQty" label="损耗量" min-width="80" />
        <el-table-column prop="shouldSendQty" label="应发数量" min-width="80" />
        <el-table-column prop="actualSendQty" label="实发数量" min-width="80" />
        <el-table-column prop="demandStock" label="需求库存" min-width="80" />
        <el-table-column prop="materialShortage" label="缺料" min-width="80" />
        <el-table-column prop="totalOnWayQty" label="在途总数" min-width="80" />
        <el-table-column prop="purchaseRequestQty" label="请购量" min-width="80" />
        <el-table-column prop="purchaseQty" label="采购量" min-width="80" />
        <el-table-column prop="purchaseRequestDate" label="请购日期" min-width="120" />
        <el-table-column prop="pmcDeliveryDate" label="PMC交期" min-width="120" />
        <el-table-column prop="purchaseDate" label="采购日期" min-width="120" />
        <el-table-column prop="purchasePromiseString" label="采购承诺交期" min-width="120" />
        <el-table-column prop="purchaseRequestUnit" label="请购单位" min-width="80" />
        <el-table-column prop="purchaseOrderNo" label="采购单号" min-width="120" />
        <el-table-column prop="storageQty" label="入库量" min-width="80" />
        <el-table-column prop="storageString" label="入库日期" min-width="120" />
        <el-table-column prop="checkQty" label="验收量" min-width="80" />
        <el-table-column prop="qualifiedQty" label="合格量" min-width="80" />
        <el-table-column prop="checkNo" label="校验单号" min-width="120" />
        <el-table-column prop="purchaseInQty" label="进货量" min-width="80" />
        <el-table-column prop="purchaseInDate" label="进货日期" min-width="120" />
        <el-table-column prop="purchaseInNo" label="进货单号" min-width="120" />
        <el-table-column prop="materialNo" label="材料编号" min-width="120" />
        <el-table-column prop="materialDate" label="材料日期" min-width="120" />
        <el-table-column prop="materialUserName" label="材料员" min-width="100" />
        <el-table-column prop="returnQty" label="退货量" min-width="80" />
        <el-table-column prop="returnData" label="退货日期" min-width="120" />
        <el-table-column prop="returnCheckNo" label="退货验收单号" min-width="120" />
        <el-table-column prop="brand" label="品牌" min-width="100" />
        <el-table-column prop="bomRemark" label="BOM备注" min-width="120" />
        <el-table-column prop="materialCategory" label="材料类别" min-width="100" />
        <el-table-column prop="categoryExplain" label="类别说明" min-width="120" />
        <el-table-column prop="closed" label="是否关闭" min-width="100" />
        <el-table-column prop="warehouse" label="仓库" min-width="100" />
        <el-table-column prop="customerCode" label="客户代码" min-width="100" />
        <el-table-column prop="projectNo" label="项目编号" min-width="100" />
        <el-table-column prop="projectNode" label="项目节点" min-width="100" />
        <el-table-column prop="location" label="区域" min-width="100" />
        <el-table-column prop="salesOrderNo" label="销售订单号" min-width="120" />
        <el-table-column prop="oldMaterialNo" label="旧料号" min-width="120" />
        <el-table-column prop="stockQty" label="库存量" min-width="80" />
        <el-table-column prop="orderUser" label="下单员" min-width="100" />
        <el-table-column prop="bomEngineer" label="BOM工程师" min-width="120" />
        <el-table-column prop="vmiSafetyStock" label="VMI安全库存" min-width="100" />
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="materialInfoVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue';
import {
  ElButton,
  ElCard,
  ElDatePicker,
  ElDialog,
  ElForm,
  ElFormItem,
  ElIcon,
  ElInput,
  ElMessage,
  ElPagination,
  ElSelect,
  ElTable,
  ElTableColumn
} from 'element-plus';
import { Delete, Download, Edit, Refresh, Search } from '@element-plus/icons-vue';
import { exportToExcel } from '@/utils/excel';
import { getMerchandiserList } from '@/api/pmc/merchandiser';
import { getProfitCenterList } from '@/api/pmc/statis2';
import { checkPermi } from '@/utils/permission';
import { useUserStore } from '@/store/modules/user';
import { editStatis6Data, getMaterialData, getStatis6Data, loadStatis6Data } from '@/api/pmc/statis6';
import { getPicUrl, getStatis5Data3 } from '@/api/pmc/statis5';

// 搜索表单数据
const searchForm = reactive({
  bargainDate: [],
  deliveryDate: [],
  customerCode: '',
  drNumber: '',
  moNumber: '',
  profitCenter: '',
  merchandiser: '',
  assemblyTechnician: '',
  status: '已装配',
  isClosed: 'N'
});

// 响应式数据
const loading = ref(false);
const exportLoading = ref(false);
const enableSort = ref(false);
const totalRecords = ref(0);
const tableRef = ref();

// 表格选择相关
const selectedRows = ref<any[]>([]);
const selectedRow = ref<string | number>(""); // 单选选中的行ID

const handleSelectionChange = (rows: any[]) => {
  selectedRows.value = rows;
};

const handleRowClick = (row: any) => {
  // 直接选择当前行，Element Plus会自动处理单选逻辑
  tableRef.value?.setCurrentRow(row);
  selectedRow.value = row.id;
};

const handleCurrentChange = (row: any) => {
  // 当前行变化时，同步选中状态
  if (row) {
    selectedRow.value = row.id;
  }
};

const handleRadioChange = (row: any) => {
  // 单选按钮变化时，设置当前行
  tableRef.value?.setCurrentRow(row);
  // 清空多选
  selectedRows.value = [row];
};

const checkSelectable = () => {
  // 允许所有行被选择
  return true;
};

const enable = ref(false);
const mData = ref<string[]>([]);
const profitCenters = ref<string[]>([]);

// 编辑对话框相关
const editDialogVisible = ref(false);
const editForm = ref({
  id: '',
  moNo: '',
  assemblerExceptionRemark: '',
  qaExceptionRemark: '',
  pmcRemark: '',
  pmcNotifyAssembly: '',
  remark: ''
});

// 扫描记录对话框相关
const scanRecordVisible = ref(false);
const scanRecords = ref([]);
const scanExportLoading = ref(false);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);

// 模拟数据

// 初始化为空数组
const tableData = ref([]);

// 下拉选项数据
// const merchandiserList = ref(['庾燕君', '肖伟', '黎配威', '耿尧']);
// const profitCenters = ref(['PC001', 'PC002', 'PC003']);

// 计算分页后的数据
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return tableData.value.slice(start, end);
});

// 加载负责人数据
const loadMerchandiserData = async () => {
  try {
    const res = await getMerchandiserList();
    mData.value = res.data;
  } catch (err: any) {
    ElMessage.error(err.message);
  }
};

// 加载利润中心数据
const loadProfitCenterData = async () => {
  try {
    const res = await getProfitCenterList();
    profitCenters.value = res.data;
  } catch (err: any) {
    ElMessage.error(err.message);
  }
};

// 查询方法
const handleQuery = async () => {
  loading.value = true;
  try {
    // 调用实际的API查询数据
    const res = await getStatis6Data(searchForm);
    tableData.value = res.data || [];

    totalRecords.value = tableData.value.length;
    currentPage.value = 1;
    ElMessage.success('查询成功');
  } catch (err: any) {
    ElMessage.error('查询失败：' + (err.message || '未知错误'));
  } finally {
    loading.value = false;
  }
};

// 修改方法
const handleModify = () => {
  // 检查是否有选中的行
  if (!selectedRow.value) {
    ElMessage.warning('请先选择要修改的记录');
    return;
  }

  // 获取选中的行数据
  const selectedRowData = tableData.value.find(item => item.id === selectedRow.value);

  if (!selectedRowData) {
    ElMessage.warning('未找到选中的记录');
    return;
  }

  // 初始化编辑表单
  editForm.value = {
    ...selectedRowData,
    pmcNotifyAssembly: selectedRowData.pmcNotifyAssembly || '',
    assemblyStartTime: selectedRowData.assemblyStartTime || ''
  };

  // 显示对话框
  editDialogVisible.value = true;
};

// 确认编辑方法
const confirmEdit = async () => {
  try {
    // 调用保存API更新数据
    await editStatis6Data(editForm.value);

    // 更新表格数据
    const index = tableData.value.findIndex((item) => item.id === editForm.value.id);

    if (index !== -1) {
      tableData.value[index] = {
        ...tableData.value[index],
        ...editForm.value
      };
    }

    ElMessage.success('修改成功');
    // 关闭对话框
    editDialogVisible.value = false;
  } catch (err: any) {
    ElMessage.error('修改失败：' + (err.message || '未知错误'));
  }
};

// 对话框关闭处理
const handleClose = (done: () => void) => {
  ElMessage.info('编辑已取消');
  done();
};

// 导出方法
const handleExport = async () => {
  if (tableData.value.length === 0) {
    ElMessage.warning('没有数据可以导出');
    return;
  }

  exportLoading.value = true;
  try {
    // 直接使用原始数据结构，完全匹配表格列定义
    const exportData = tableData.value.map((item) => ({
      'ID': item.id,
      '状态': item.status,
      'MO号': item.moNo,
      'DR项次': item.drItm,
      '图号': item.drawNumber,
      '客户代码': item.custNo,
      '接单日期': item.orderDate,
      '客户PO号': item.custPoNo,
      '客户要求交期': item.deliveryDate,
      'PMC要求交期': item.pmcReqDate,
      '订单数量': item.orderQty,
      '末工序时间': item.lastOpTime,
      '装配异常备注': item.assemblerExceptionRemark,
      'QA异常备注': item.qaExceptionRemark,
      'PMC备注': item.pmcRemark,
      '下单员姓名': item.ordererName,
      '跟单负责人': item.merchandiser,
      '装配技师': item.assembler,
      '研发工程师': item.bomCharge,
      'PMC通知装配领料': item.pmcNotifyAssembly,
      '装配领料开工时间': item.assemblyStartTime,
      '零件BOM清单': item.partBomCount,
      '标准件BOM清单': item.standardBomCount,
      '生产图纸总数': item.totalProductionDrawings,
      '生产未完成图纸数': item.uncompletedProduction,
      '外发图纸总数': item.totalOutsourced,
      '外发图纸未完成数': item.uncompletedOutsourced,
      '钣金图纸总数': item.totalSheetMetal,
      '钣金图纸未完成数': item.uncompletedSheetMetal,
      'QA图纸数': item.qaDrawingCount,
      '标准件总项数': item.totalStandardParts,
      '标准件未完成项数': item.uncompletedStandardParts,
      'SO_NO': item.soNo,
      'SO_ITM': item.soItm,
      '利润中心': item.profitCenter,
      '零件分类': item.partType,
      '计划类型': item.planType,
      '状态1': item.status1,
      '备注': item.remark
    }));

    // 精确匹配表格列顺序和字段名
    const headers = {
      'ID': 'ID',
      '状态': '状态',
      'MO号': 'MO号',
      'DR项次': 'DR项次',
      '图号': '图号',
      '客户代码': '客户代码',
      '接单日期': '接单日期',
      '客户PO号': '客户PO号',
      '客户要求交期': '客户要求交期',
      'PMC要求交期': 'PMC要求交期',
      '订单数量': '订单数量',
      '末工序时间': '末工序时间',
      '装配异常备注': '装配异常备注',
      'QA异常备注': 'QA异常备注',
      'PMC备注': 'PMC备注',
      '下单员姓名': '下单员姓名',
      '跟单负责人': '跟单负责人',
      '装配技师': '装配技师',
      '研发工程师': '研发工程师',
      'PMC通知装配领料': 'PMC通知装配领料',
      '装配领料开工时间': '装配领料开工时间',
      '零件BOM清单': '零件BOM清单',
      '标准件BOM清单': '标准件BOM清单',
      '生产图纸总数': '生产图纸总数',
      '生产未完成图纸数': '生产未完成图纸数',
      '外发图纸总数': '外发图纸总数',
      '外发图纸未完成数': '外发图纸未完成数',
      '钣金图纸总数': '钣金图纸总数',
      '钣金图纸未完成数': '钣金图纸未完成数',
      'QA图纸数': 'QA图纸数',
      '标准件总项数': '标准件总项数',
      '标准件未完成项数': '标准件未完成项数',
      'SO_NO': 'SO_NO',
      'SO_ITM': 'SO_ITM',
      '利润中心': '利润中心',
      '零件分类': '零件分类',
      '计划类型': '计划类型',
      '状态1': '状态1',
      '备注': '备注'
    };

    const fileName = `装配部治具装配状态一览表_${new Date().getTime()}`;
    await exportToExcel(exportData, headers, fileName, '装配部治具装配状态一览表', 'E6A23C');
    ElMessage.success('导出成功');
  } catch (err: any) {
    ElMessage.error('导出失败：' + err.message);
  } finally {
    exportLoading.value = false;
  }
};

// 加载新数据方法
const handleLoadNewData = async () => {
  loading.value = true;
  try {
    // 调用实际的API重新加载数据
    const res = await loadStatis6Data();
    ElMessage.success('数据已刷新');
  } catch (err: any) {
    ElMessage.error('加载新数据失败：' + (err.message || '未知错误'));
  } finally {
    loading.value = false;
  }
};

// 重置表单方法
const handleReset = () => {
  // 重置搜索表单为初始值
  searchForm.bargainDate = [];
  searchForm.deliveryDate = [];
  searchForm.customerCode = '';
  searchForm.drNumber = '';
  searchForm.moNumber = '';
  searchForm.profitCenter = '';
  searchForm.merchandiser = '';
  searchForm.assemblyTechnician = '';
  searchForm.status = '已装配';
  searchForm.isClosed = 'N';

  ElMessage.success('表单已重置');
};

// 处理分页变化
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  currentPage.value = 1;
};

// 查看图纸
const handleViewPic = async (row: any) => {
  const moNo = row?.moNo || row?.moNumber;
  const drawNo = row?.drawNumber || row?.drawingNumber;
  if (!moNo || !drawNo) {
    ElMessage.warning('缺少MO号或图号');
    return;
  }

  await getPicUrl({ moNumber: moNo, drawNumber: drawNo })
    .then((res) => {
      if (res.data === 'DATA_NOT_FOUND') {
        window.open(`https://tuku.world-machining.com/sap_drawing/${moNo}.pdf`, '_blank');
      } else {
        window.open(res.data, '_blank');
      }
    })
    .catch((error) => {
      ElMessage.error(error);
    });
};

// 查看扫描记录
const viewScanRecords = async (row: any) => {
  try {
    const res = await getStatis5Data3({ moNumber: row.moNo });
    scanRecords.value = res.data || [];
    scanRecordVisible.value = true;
  } catch (err: any) {
    ElMessage.error('获取扫描记录失败：' + err.message);
  }
};

// 扫描记录导出方法
const handleExportScanRecords = async () => {
  if (scanRecords.value.length === 0) {
    ElMessage.warning('没有数据可以导出');
    return;
  }

  scanExportLoading.value = true;
  try {
    const exportData = scanRecords.value.map((item) => ({
      'ID': item.id,
      'PJ号': item.pjNumber,
      '工序号': item.processNumber,
      '工序名称': item.processName,
      '计划类型': item.planType,
      '标识': item.identifier,
      '操作员': item.operator,
      '姓名': item.name,
      '机床': item.machineTool,
      '动作': item.action,
      '开始时间': item.startTime,
      '结束时间': item.endTime,
      '通过数量': item.passQuantity,
      '报废数量': item.scrapQuantity,
      '总进度': item.totalProgress,
      '当前进度': item.currentProgress,
      '工艺工时': item.processHours,
      '实际时间': item.actualTime,
      '备注': item.remark,
      '作法描述': item.methodDescription,
      '供应商': item.supplier,
      '送货单号': item.deliveryNoteNumber,
      '送货时间': item.deliveryTime,
      '送检单号': item.inspectionNoteNumber,
      '收货凭证': item.receiptVoucher,
      'BAT_NO': item.batNo,
      '订单类别': item.orderCategory,
      '客户代码': item.customerCode,
      '炉号': item.furnaceNumber,
      '加工厂址': item.processingPlant,
      '过账日期': item.postingDate,
      '移动类型': item.movementType,
      '公司': item.company,
      '工厂': item.factory,
      '物料编号': item.materialNumber,
      '物料描述': item.materialDescription,
      '库存地点': item.storageLocation,
      '采购PO号': item.purchasePoNumber,
      '行项目': item.lineItem,
      '同批批号': item.sameBatchNumber,
      '单位': item.unit,
      '成本中心': item.costCenter,
      '图号': item.drawingNumber,
      '版本': item.version,
      '多余数量': item.excessQuantity,
      '行业': item.industry,
      'FHID': item.fhid,
      '送货单': item.deliveryNote,
      'DN单号': item.dnNumber,
      'DN行号': item.dnLineNumber,
      '部门代号': item.departmentCode,
      '部门': item.department,
      '库位': item.storagePosition,
      '领料人': item.materialRequester,
      '领料姓名': item.materialRequesterName,
      '出厂申请': item.factoryExitApplication,
      '零件分类': item.partClassification,
      '品号': item.productNumber,
      '是否合单': item.isCombinedOrder
    }));

    const headers = {
      'ID': 'ID',
      'PJ号': 'PJ号',
      '工序号': '工序号',
      '工序名称': '工序名称',
      '计划类型': '计划类型',
      '标识': '标识',
      '操作员': '操作员',
      '姓名': '姓名',
      '机床': '机床',
      '动作': '动作',
      '开始时间': '开始时间',
      '结束时间': '结束时间',
      '通过数量': '通过数量',
      '报废数量': '报废数量',
      '总进度': '总进度',
      '当前进度': '当前进度',
      '工艺工时': '工艺工时',
      '实际时间': '实际时间',
      '备注': '备注',
      '作法描述': '作法描述',
      '供应商': '供应商',
      '送货单号': '送货单号',
      '送货时间': '送货时间',
      '送检单号': '送检单号',
      '收货凭证': '收货凭证',
      'BAT_NO': 'BAT_NO',
      '订单类别': '订单类别',
      '客户代码': '客户代码',
      '炉号': '炉号',
      '加工厂址': '加工厂址',
      '过账日期': '过账日期',
      '移动类型': '移动类型',
      '公司': '公司',
      '工厂': '工厂',
      '物料编号': '物料编号',
      '物料描述': '物料描述',
      '库存地点': '库存地点',
      '采购PO号': '采购PO号',
      '行项目': '行项目',
      '同批批号': '同批批号',
      '单位': '单位',
      '成本中心': '成本中心',
      '图号': '图号',
      '版本': '版本',
      '多余数量': '多余数量',
      '行业': '行业',
      'FHID': 'FHID',
      '送货单': '送货单',
      'DN单号': 'DN单号',
      'DN行号': 'DN行号',
      '部门代号': '部门代号',
      '部门': '部门',
      '库位': '库位',
      '领料人': '领料人',
      '领料姓名': '领料姓名',
      '出厂申请': '出厂申请',
      '零件分类': '零件分类',
      '品号': '品号',
      '是否合单': '是否合单'
    };

    const fileName = `扫描记录_${new Date().getTime()}`;
    await exportToExcel(exportData, headers, fileName, '扫描记录', '409EFF');
    ElMessage.success('导出成功');
  } catch (err: any) {
    ElMessage.error('导出失败：' + err.message);
  } finally {
    scanExportLoading.value = false;
  }
};

// 查看物料信息对话框相关
const materialInfoVisible = ref(false);
const materialInfoList = ref([]); // 存储物料信息列表
const materialInfoLoading = ref(false); // 物料信息加载状态

// 查看物料信息
const viewMaterialInfo = async (row: any) => {
  try {
    materialInfoLoading.value = true;
    // 调用API获取物料信息
    const res = await getMaterialData({ moNumber: row.moNo });
    if (res.data && res.data.length > 0) {
      // 使用API返回的数据列表
      materialInfoList.value = res.data;
    } else {
      // 如果没有返回数据，则清空列表
      materialInfoList.value = [];
    }
    materialInfoVisible.value = true;
  } catch (err: any) {
    ElMessage.error('获取物料信息失败：' + err.message);
  } finally {
    materialInfoLoading.value = false;
  }
};

// 初始化
onMounted(() => {
  loadProfitCenterData();
  if (checkPermi(['pmc:mo:statis1'])) {
    loadMerchandiserData();
  } else {
    searchForm.merchandiser = useUserStore().nickname;
    enable.value = true;
  }
});
</script>

<style scoped>
.el-date-picker {
  width: 240px;
}

.el-select {
  width: 200px;
}

.el-input {
  width: 100%;
}

.loading {
  cursor: wait !important;
  opacity: 0.6;
}

.loading::after {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  border: 4px solid transparent;
  border-top-color: #409eff;
  border-radius: 50%;
  animation: rotation 1s linear infinite;
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
