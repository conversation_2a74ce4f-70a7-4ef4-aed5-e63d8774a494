<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="动作" prop="action">
              <el-select v-model="queryParams.action" placeholder="请选择动作" clearable>
                <el-option v-for="dict in scan_action" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="机床名称" prop="machines">
              <el-select v-model="queryParams.machines" multiple collapse-tags placeholder="请选择机床">
                <el-option v-for="item in allMachines" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['tianxin:machinescan:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="machineScanList">
        <el-table-column label="机台号" :width="ColumnWidthNarrow" align="center" prop="machineNo" />
        <el-table-column label="客户代码" align="center" prop="customerCode" />
        <el-table-column label="MO号" align="center" prop="moNo" />
        <el-table-column label="工序号" align="center" prop="processNo" />
        <el-table-column label="工序名称" :width="ColumnWidthNormal" align="center" prop="processName" />
        <el-table-column label="操作员" align="center" prop="operator" />
        <el-table-column label="姓名" align="center" prop="operatorName" />
        <el-table-column label="动作" align="center" prop="action" />
        <el-table-column label="开始时间" :width="ColumnWidthNormal" align="center" prop="startTime" />
        <el-table-column label="结束时间" align="center" prop="endTime" />
        <el-table-column label="通过数量" align="center" prop="passedQuantity" />
        <el-table-column label="报废数量" align="center" prop="scrappedQuantity" />
        <el-table-column label="总进度" align="center" prop="totalProgress" />
        <el-table-column label="当前进度" align="center" prop="currentProgress" />
        <el-table-column label="工艺工时" align="center" prop="processManHours" />
        <el-table-column label="实际时间" align="center" prop="actualTime" />
        <el-table-column label="计划开始时间" :width="ColumnWidthNarrow" align="center" prop="plannedStartTime" />
        <el-table-column label="计划结束时间" :width="ColumnWidthNarrow" align="center" prop="plannedEndTime" />
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
  </div>
</template>

<script setup name="Machinescan" lang="ts">
import { listMachineScan, listAllMachines } from '@/api/tianxin/machinescan';
import { MachineScanVO } from '@/api/tianxin/machinescan/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_common_status, scan_action, sys_yes_no } = toRefs<any>(proxy?.useDict('sys_common_status', 'scan_action', 'sys_yes_no'));

const machineScanList = ref<MachineScanVO[]>([]);
const loading = ref(false);
const showSearch = ref(true);
const total = ref(0);
const ColumnWidthNormal = ref(200);
const ColumnWidthNarrow = ref(150);

const queryFormRef = ref<ElFormInstance>();
const allMachines = ref<any[]>([]);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  action: '',
  machines: null
});

/** 查询机床扫描查询列表 */
const getList = async () => {
  loading.value = true;
  const res = await listMachineScan(queryParams.value);
  machineScanList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

const getAllMachines = async () => {
  const res = await listAllMachines();
  if (res.data) {
    const list = [] as any[];
    res.data.map((item) => {
      list.push({
        label: item,
        value: item
      });
    });
    allMachines.value = list;
  }
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'tianxin/machinescan/export',
    {
      ...queryParams.value
    },
    `machineScan_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getAllMachines();
  if (scan_action.value && scan_action.value.length > 0) {
    queryParams.value.action = (scan_action.value[0].value as string) || '';
  }
  // getList();
});
</script>
