<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="机床" prop="machineTool">
              <el-input v-model="queryParams.machineTool" placeholder="请输入机床" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="资源" prop="resource">
              <el-input v-model="queryParams.resource" placeholder="请输入资源" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="可扫描" prop="isScannable">
              <el-select v-model="queryParams.isScannable" placeholder="请选择可扫描" clearable>
                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="类别" prop="category">
              <el-input v-model="queryParams.category" placeholder="请输入类别" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="操作人" prop="operator">
              <el-input v-model="queryParams.operator" placeholder="请输入操作人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="操作时间" style="width: 308px">
              <el-date-picker
                v-model="dateRangeOperationTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['tianxin:machineToolInfo:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['tianxin:machineToolInfo:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['tianxin:machineToolInfo:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['tianxin:machineToolInfo:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="machineToolInfoList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="主键" align="center" prop="id" v-if="false" />
        <el-table-column label="机床" align="center" prop="machineTool" />
        <el-table-column label="资源" align="center" prop="resource" />
        <el-table-column label="序号" align="center" prop="serialNo" />
        <el-table-column label="可扫描" align="center" prop="isScannable">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.isScannable" />
          </template>
        </el-table-column>
        <el-table-column label="分组" align="center" prop="grouping" />
        <el-table-column label="类别" align="center" prop="category" />
        <el-table-column label="使用状态" align="center" prop="usageStatus">
          <template #default="scope">
            <dict-tag :options="sys_common_status" :value="scope.row.usageStatus" />
          </template>
        </el-table-column>
        <el-table-column label="停用日期" align="center" prop="deactivationDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.deactivationDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remarks" />
        <el-table-column label="操作人" align="center" prop="operator" />
        <el-table-column label="操作时间" align="center" prop="operationTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.operationTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['tianxin:machineToolInfo:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button
                link
                type="primary"
                icon="Delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['tianxin:machineToolInfo:remove']"
              ></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="machineToolInfoFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="机床" prop="machineTool">
          <el-input v-model="form.machineTool" placeholder="请输入机床" />
        </el-form-item>
        <el-form-item label="资源" prop="resource">
          <el-input v-model="form.resource" placeholder="请输入资源" />
        </el-form-item>
        <el-form-item label="序号" prop="serialNo">
          <el-input v-model="form.serialNo" placeholder="请输入序号" />
        </el-form-item>
        <el-form-item label="可扫描" prop="isScannable">
          <el-select v-model="form.isScannable" placeholder="请选择可扫描">
            <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="分组" prop="grouping">
          <el-input v-model="form.grouping" placeholder="请输入分组" />
        </el-form-item>
        <el-form-item label="类别" prop="category">
          <el-input v-model="form.category" placeholder="请输入类别" />
        </el-form-item>
        <el-form-item label="使用状态" prop="usageStatus">
          <el-radio-group v-model="form.usageStatus">
            <el-radio v-for="dict in sys_common_status" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="停用日期" prop="deactivationDate">
          <el-date-picker clearable v-model="form.deactivationDate" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择停用日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="form.remarks" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="操作人" prop="operator">
          <el-input v-model="form.operator" placeholder="请输入操作人" />
        </el-form-item>
        <el-form-item label="操作时间" prop="operationTime">
          <el-date-picker clearable v-model="form.operationTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择操作时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="MachineToolInfo" lang="ts">
import {
  listMachineToolInfo,
  getMachineToolInfo,
  delMachineToolInfo,
  addMachineToolInfo,
  updateMachineToolInfo
} from '@/api/tianxin/machineToolInfo';
import { MachineToolInfoVO, MachineToolInfoQuery, MachineToolInfoForm } from '@/api/tianxin/machineToolInfo/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_common_status, sys_yes_no } = toRefs<any>(proxy?.useDict('sys_common_status', 'sys_yes_no'));

const machineToolInfoList = ref<MachineToolInfoVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRangeDeactivationDate = ref<[DateModelType, DateModelType]>(['', '']);
const dateRangeOperationTime = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const machineToolInfoFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: MachineToolInfoForm = {
  id: undefined,
  machineTool: undefined,
  resource: undefined,
  serialNo: undefined,
  isScannable: undefined,
  grouping: undefined,
  category: undefined,
  usageStatus: undefined,
  deactivationDate: undefined,
  remarks: undefined,
  operator: undefined,
  operationTime: undefined
};
const data = reactive<PageData<MachineToolInfoForm, MachineToolInfoQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    machineTool: undefined,
    resource: undefined,
    isScannable: undefined,
    category: undefined,
    operator: undefined,
    params: {
      operationTime: undefined
    }
  },
  rules: {
    // id字段在新增时不需要验证，只在编辑时验证
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeDeactivationDate.value, 'DeactivationDate');
  proxy?.addDateRange(queryParams.value, dateRangeOperationTime.value, 'OperationTime');
  const res = await listMachineToolInfo(queryParams.value);
  machineToolInfoList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  machineToolInfoFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeDeactivationDate.value = ['', ''];
  dateRangeOperationTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: MachineToolInfoVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: MachineToolInfoVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getMachineToolInfo(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等';
};

/** 提交按钮 */
const submitForm = () => {
  machineToolInfoFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateMachineToolInfo(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addMachineToolInfo(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: MachineToolInfoVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal
    .confirm('是否确认删除机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等编号为"' + _ids + '"的数据项？')
    .finally(() => (loading.value = false));
  await delMachineToolInfo(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'machineToolInfo/machineToolInfo/export',
    {
      ...queryParams.value
    },
    `machineToolInfo_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
