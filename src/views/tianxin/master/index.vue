<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="MO_NO" prop="moNo">
              <el-input v-model="queryParams.moNo" placeholder="请输入MO_NO" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="客户PO" prop="customerPoNo">
              <el-input v-model="queryParams.customerPoNo" placeholder="请输入客户PO号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="订单状态" prop="sta">
              <el-input v-model="queryParams.sta" placeholder="请输入订单状态" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="客户代码" prop="customerCode">
              <el-input v-model="queryParams.customerCode" placeholder="请输入客户代码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="图号" prop="dwgNo">
              <el-input v-model="queryParams.dwgNo" placeholder="请输入图号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="完工状态" prop="finishStatus">
              <el-select v-model="queryParams.finishStatus" placeholder="请选择完工状态">
                <el-option label="未完成" value="未完成"></el-option>
                <el-option label="已完成" value="已完成"></el-option>
                <el-option label="全部" value="全部"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="4">
            <el-form-item label="处理方式" prop="processMethod">
              <el-select v-model="processMethod" placeholder="请选择处理方式">
                <el-option label="待更新图纸" value="待更新图纸"></el-option>
                <el-option label="已更新图纸" value="已更新图纸"></el-option>
                <el-option label="图纸退研发" value="图纸退研发"></el-option>
                <el-option label="PMC送图" value="PMC送图"></el-option>
                <el-option label="合并供料" value="合并供料"></el-option>
                <el-option label="取消供料" value="取消供料"></el-option>
                <el-option label="撤消排产" value="撤消排产"></el-option>
                <el-option label="暂停" value="暂停"></el-option>
                <el-option label="关闭" value="关闭"></el-option>
                <el-option label="恢复" value="恢复"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="1.2">
            <el-button type="primary" @click="handleSubmit">提交</el-button>
          </el-col>
          <el-col :span="1">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['tianxin:master:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="masterList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" width="80" v-if="false" />
        <el-table-column label="MO_NO" align="center" prop="moNo" width="120">
          <template #default="scope">
            <span :class="{ 'mo-no-green': scope.row.hasStandardParts }" class="mo-no-text">
              {{ scope.row.moNo }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="项目号" align="center" prop="projectNo" width="120" />
        <el-table-column label="项目经理" align="center" prop="projectManager" width="100" />
        <el-table-column label="客户PO号" align="center" prop="customerPoNo" width="120" />
        <el-table-column label="PO_ITM" align="center" prop="poItm" width="80" />
        <el-table-column label="SO号" align="center" prop="soNo" width="120" />
        <el-table-column label="SO项次" align="center" prop="soItm" width="80" />
        <el-table-column label="客户代码" align="center" prop="customerCode" width="100" />
        <el-table-column label="上层订单号" align="center" prop="parentOrderNo" width="120" />
        <el-table-column label="零件分类" align="center" prop="partClassification" width="100" />
        <el-table-column label="品号" align="center" prop="prdNo" width="120" />
        <el-table-column label="图号" align="center" prop="dwgNo" width="120" />
        <el-table-column label="版本" align="center" prop="version" width="80" />
        <el-table-column label="订单状态" align="center" prop="sta" width="100" />
        <el-table-column label="备注信息" align="center" prop="rem" width="150" show-overflow-tooltip />
        <el-table-column label="接单日期" align="center" prop="orderDate" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope.row.orderDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="客户要求交期" align="center" prop="customerReqDeliveryDate" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope.row.customerReqDeliveryDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="承诺交期" align="center" prop="promisedDeliveryDate" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope.row.promisedDeliveryDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="下计划日期" align="center" prop="nextPlanDate" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope.row.nextPlanDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="要求完成日期" align="center" prop="requiredCompletionDate" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope.row.requiredCompletionDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="末工序时间" align="center" prop="lastProcessTime" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope.row.lastProcessTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="入仓日期" align="center" prop="warehouseEntryDate" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope.row.warehouseEntryDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="实际交期" align="center" prop="actualDeliveryDate" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope.row.actualDeliveryDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>

        <el-table-column label="客户产品料号" align="center" prop="customerProductNo" width="120" />
        <el-table-column label="客户产品名称" align="center" prop="customerProductName" width="150" show-overflow-tooltip />
        <el-table-column label="产品描述" align="center" prop="productDescription" width="200" show-overflow-tooltip />
        <el-table-column label="订单数量" align="center" prop="orderQuantity" width="100" />
        <el-table-column label="生产数量" align="center" prop="productionQuantity" width="100" />
        <el-table-column label="工艺数量" align="center" prop="processQuantity" width="100" />
        <el-table-column label="报废数量" align="center" prop="scrapQuantity" width="100" />
        <el-table-column label="已入仓数" align="center" prop="inWarehouseCount" width="100" />
        <el-table-column label="欠入仓数" align="center" prop="outstandingWarehouseCount" width="100" />
        <el-table-column label="交货数量" align="center" prop="deliveryQuantity" width="100" />
        <el-table-column label="欠出货数量" align="center" prop="outstandingShipmentQuantity" width="100" />
        <el-table-column label="单件净重" align="center" prop="singlePieceNetWeight" width="100" />
        <el-table-column label="单位" align="center" prop="unit" width="80" />
        <el-table-column label="行业" align="center" prop="industry" width="100" />
        <el-table-column label="订单类别" align="center" prop="orderCategory" width="100" />
        <el-table-column label="计划类型" align="center" prop="planType" width="100" />
        <el-table-column label="下单员" align="center" prop="orderPlacer" width="100" />
        <el-table-column label="跟单员" align="center" prop="followUpStaff" width="100" />
        <el-table-column label="BOM负责人" align="center" prop="bomManager" width="100" />
        <el-table-column label="工艺编制人" align="center" prop="processCompiler" width="100" />
        <el-table-column label="合单标识" align="center" prop="mergeFlag" width="100" />
        <el-table-column label="急单标识" align="center" prop="urgentFlag" width="100" />
        <el-table-column label="打样标识" align="center" prop="sampleFlag" width="100" />
        <el-table-column label="是否有标准件" align="center" prop="hasStandardParts" width="120" />
        <el-table-column label="是否买料" align="center" prop="isMaterialPurchased" width="100" />
        <el-table-column label="是否电镀" align="center" prop="isElectroplated" width="100" />
        <el-table-column label="动作" align="center" prop="action" width="100" />
        <el-table-column label="当前工序" align="center" prop="currentProcess" width="120" />
        <el-table-column label="加工材料" align="center" prop="processingMaterial" width="120" />
        <el-table-column label="要求材料" align="center" prop="requiredMaterial" width="120" />
        <el-table-column label="报价材料" align="center" prop="quotedMaterial" width="120" />
        <el-table-column label="图纸难度等级" align="center" prop="drawingDifficultyLevel" width="120" />
        <el-table-column label="工艺难度等级" align="center" prop="processDifficultyLevel" width="120" />
        <el-table-column label="工艺版本" align="center" prop="processVersion" width="100" />
        <el-table-column label="完成部门 (供应商)" align="center" prop="completionDepartment" width="150" />
        <el-table-column label="生产车间 (加工车间)" align="center" prop="productionWorkshop" width="150" />
        <el-table-column label="利润中心" align="center" prop="profitCenter" width="100" />
        <el-table-column label="成本中心" align="center" prop="costCenter" width="100" />
        <el-table-column label="收费备注" align="center" prop="chargeRemarks" width="150" show-overflow-tooltip />
        <el-table-column label="关单原因" align="center" prop="closeOrderReason" width="150" show-overflow-tooltip />
        <el-table-column label="状态1" align="center" prop="sta1" width="100" />
        <el-table-column label="SO单 (同步标记)" align="center" prop="soDocument" width="120" />
        <el-table-column label="MO号 (同步标记)" align="center" prop="moDocument" width="120" />
        <el-table-column label="PMC分组 (同步标记)" align="center" prop="pmcGroup" width="120" />
        <el-table-column label="工艺 (同步标记)" align="center" prop="process" width="120" />
        <el-table-column label="排产 (同步标记)" align="center" prop="productionScheduling" width="120" />
        <el-table-column label="MRP (同步标记)" align="center" prop="mrp" width="120" />
        <el-table-column label="请购 (同步标记)" align="center" prop="purchaseRequest" width="120" />
        <el-table-column label="下采购单 (同步标记)" align="center" prop="purchaseOrder" width="120" />
        <el-table-column label="材料收货 (同步标记)" align="center" prop="materialReceipt" width="120" />
        <el-table-column label="IQC检测 (同步标记)" align="center" prop="iqcInspection" width="120" />
        <el-table-column label="材料退货 (同步标记)" align="center" prop="materialReturn" width="120" />
        <el-table-column label="材料进仓 (同步标记)" align="center" prop="materialInbound" width="120" />
        <el-table-column label="托工 (同步标记)" align="center" prop="subcontracting" width="120" />
        <el-table-column label="请购单号 (同步天心单号)" align="center" prop="purchaseRequestNo" width="150" />
        <el-table-column label="采购单号 (同步天心单号)" align="center" prop="purchaseOrderNo" width="150" />
        <el-table-column label="入库单号 (同步天心单号)" align="center" prop="warehouseEntryNo" width="150" />
        <el-table-column label="出库单号 (同步天心单号)" align="center" prop="warehouseExitNo" width="150" />
        <el-table-column label="销货单号 (同步天心单号)" align="center" prop="salesOrderNo" width="150" />
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改PMC订单变更管理对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="masterFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="订单状态" prop="sta">
          <el-input v-model="form.sta" placeholder="请输入订单状态" />
        </el-form-item>
        <el-form-item label="备注信息" prop="rem">
          <el-input v-model="form.rem" placeholder="请输入备注信息" />
        </el-form-item>
        <el-form-item label="接单日期" prop="orderDate">
          <el-date-picker clearable v-model="form.orderDate" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择接单日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="客户要求交期" prop="customerReqDeliveryDate">
          <el-date-picker
            clearable
            v-model="form.customerReqDeliveryDate"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择客户要求交期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="承诺交期" prop="promisedDeliveryDate">
          <el-date-picker
            clearable
            v-model="form.promisedDeliveryDate"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择承诺交期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="下计划日期" prop="nextPlanDate">
          <el-date-picker clearable v-model="form.nextPlanDate" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择下计划日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="要求完成日期" prop="requiredCompletionDate">
          <el-date-picker
            clearable
            v-model="form.requiredCompletionDate"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择要求完成日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="末工序时间" prop="lastProcessTime">
          <el-date-picker clearable v-model="form.lastProcessTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择末工序时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="入仓日期" prop="warehouseEntryDate">
          <el-date-picker clearable v-model="form.warehouseEntryDate" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择入仓日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="实际交期" prop="actualDeliveryDate">
          <el-date-picker clearable v-model="form.actualDeliveryDate" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择实际交期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="客户代码" prop="customerCode">
          <el-input v-model="form.customerCode" placeholder="请输入客户代码" />
        </el-form-item>
        <el-form-item label="项目号" prop="projectNo">
          <el-input v-model="form.projectNo" placeholder="请输入项目号" />
        </el-form-item>
        <el-form-item label="项目经理" prop="projectManager">
          <el-input v-model="form.projectManager" placeholder="请输入项目经理" />
        </el-form-item>
        <el-form-item label="客户PO号" prop="customerPoNo">
          <el-input v-model="form.customerPoNo" placeholder="请输入客户PO号" />
        </el-form-item>
        <el-form-item label="PO_ITM" prop="poItm">
          <el-input v-model="form.poItm" placeholder="请输入PO_ITM" />
        </el-form-item>
        <el-form-item label="SO号" prop="soNo">
          <el-input v-model="form.soNo" placeholder="请输入SO号" />
        </el-form-item>
        <el-form-item label="SO项次" prop="soItm">
          <el-input v-model="form.soItm" placeholder="请输入SO项次" />
        </el-form-item>
        <el-form-item label="MO_NO" prop="moNo">
          <el-input v-model="form.moNo" placeholder="请输入MO_NO" />
        </el-form-item>
        <el-form-item label="上层订单号" prop="parentOrderNo">
          <el-input v-model="form.parentOrderNo" placeholder="请输入上层订单号" />
        </el-form-item>
        <el-form-item label="零件分类" prop="partClassification">
          <el-input v-model="form.partClassification" placeholder="请输入零件分类" />
        </el-form-item>
        <el-form-item label="品号" prop="prdNo">
          <el-input v-model="form.prdNo" placeholder="请输入品号" />
        </el-form-item>
        <el-form-item label="图号" prop="dwgNo">
          <el-input v-model="form.dwgNo" placeholder="请输入图号" />
        </el-form-item>
        <el-form-item label="版本" prop="version">
          <el-input v-model="form.version" placeholder="请输入版本" />
        </el-form-item>
        <el-form-item label="客户产品料号" prop="customerProductNo">
          <el-input v-model="form.customerProductNo" placeholder="请输入客户产品料号" />
        </el-form-item>
        <el-form-item label="客户产品名称" prop="customerProductName">
          <el-input v-model="form.customerProductName" placeholder="请输入客户产品名称" />
        </el-form-item>
        <el-form-item label="产品描述" prop="productDescription">
          <el-input v-model="form.productDescription" placeholder="请输入产品描述" />
        </el-form-item>
        <el-form-item label="订单数量" prop="orderQuantity">
          <el-input v-model="form.orderQuantity" placeholder="请输入订单数量" />
        </el-form-item>
        <el-form-item label="生产数量" prop="productionQuantity">
          <el-input v-model="form.productionQuantity" placeholder="请输入生产数量" />
        </el-form-item>
        <el-form-item label="工艺数量" prop="processQuantity">
          <el-input v-model="form.processQuantity" placeholder="请输入工艺数量" />
        </el-form-item>
        <el-form-item label="报废数量" prop="scrapQuantity">
          <el-input v-model="form.scrapQuantity" placeholder="请输入报废数量" />
        </el-form-item>
        <el-form-item label="已入仓数" prop="inWarehouseCount">
          <el-input v-model="form.inWarehouseCount" placeholder="请输入已入仓数" />
        </el-form-item>
        <el-form-item label="欠入仓数" prop="outstandingWarehouseCount">
          <el-input v-model="form.outstandingWarehouseCount" placeholder="请输入欠入仓数" />
        </el-form-item>
        <el-form-item label="交货数量" prop="deliveryQuantity">
          <el-input v-model="form.deliveryQuantity" placeholder="请输入交货数量" />
        </el-form-item>
        <el-form-item label="欠出货数量" prop="outstandingShipmentQuantity">
          <el-input v-model="form.outstandingShipmentQuantity" placeholder="请输入欠出货数量" />
        </el-form-item>
        <el-form-item label="单件净重" prop="singlePieceNetWeight">
          <el-input v-model="form.singlePieceNetWeight" placeholder="请输入单件净重" />
        </el-form-item>
        <el-form-item label="单位" prop="unit">
          <el-input v-model="form.unit" placeholder="请输入单位" />
        </el-form-item>
        <el-form-item label="行业" prop="industry">
          <el-input v-model="form.industry" placeholder="请输入行业" />
        </el-form-item>
        <el-form-item label="订单类别" prop="orderCategory">
          <el-input v-model="form.orderCategory" placeholder="请输入订单类别" />
        </el-form-item>
        <el-form-item label="下单员" prop="orderPlacer">
          <el-input v-model="form.orderPlacer" placeholder="请输入下单员" />
        </el-form-item>
        <el-form-item label="跟单员" prop="followUpStaff">
          <el-input v-model="form.followUpStaff" placeholder="请输入跟单员" />
        </el-form-item>
        <el-form-item label="BOM负责人" prop="bomManager">
          <el-input v-model="form.bomManager" placeholder="请输入BOM负责人" />
        </el-form-item>
        <el-form-item label="工艺编制人" prop="processCompiler">
          <el-input v-model="form.processCompiler" placeholder="请输入工艺编制人" />
        </el-form-item>
        <el-form-item label="合单标识" prop="mergeFlag">
          <el-input v-model="form.mergeFlag" placeholder="请输入合单标识" />
        </el-form-item>
        <el-form-item label="急单标识" prop="urgentFlag">
          <el-input v-model="form.urgentFlag" placeholder="请输入急单标识" />
        </el-form-item>
        <el-form-item label="打样标识" prop="sampleFlag">
          <el-input v-model="form.sampleFlag" placeholder="请输入打样标识" />
        </el-form-item>
        <el-form-item label="是否有标准件" prop="hasStandardParts">
          <el-input v-model="form.hasStandardParts" placeholder="请输入是否有标准件" />
        </el-form-item>
        <el-form-item label="是否买料" prop="isMaterialPurchased">
          <el-input v-model="form.isMaterialPurchased" placeholder="请输入是否买料" />
        </el-form-item>
        <el-form-item label="是否电镀" prop="isElectroplated">
          <el-input v-model="form.isElectroplated" placeholder="请输入是否电镀" />
        </el-form-item>
        <el-form-item label="动作" prop="action">
          <el-input v-model="form.action" placeholder="请输入动作" />
        </el-form-item>
        <el-form-item label="当前工序" prop="currentProcess">
          <el-input v-model="form.currentProcess" placeholder="请输入当前工序" />
        </el-form-item>
        <el-form-item label="加工材料" prop="processingMaterial">
          <el-input v-model="form.processingMaterial" placeholder="请输入加工材料" />
        </el-form-item>
        <el-form-item label="要求材料" prop="requiredMaterial">
          <el-input v-model="form.requiredMaterial" placeholder="请输入要求材料" />
        </el-form-item>
        <el-form-item label="报价材料" prop="quotedMaterial">
          <el-input v-model="form.quotedMaterial" placeholder="请输入报价材料" />
        </el-form-item>
        <el-form-item label="图纸难度等级" prop="drawingDifficultyLevel">
          <el-input v-model="form.drawingDifficultyLevel" placeholder="请输入图纸难度等级" />
        </el-form-item>
        <el-form-item label="工艺难度等级" prop="processDifficultyLevel">
          <el-input v-model="form.processDifficultyLevel" placeholder="请输入工艺难度等级" />
        </el-form-item>
        <el-form-item label="工艺版本" prop="processVersion">
          <el-input v-model="form.processVersion" placeholder="请输入工艺版本" />
        </el-form-item>
        <el-form-item label="完成部门 (供应商)" prop="completionDepartment">
          <el-input v-model="form.completionDepartment" placeholder="请输入完成部门 (供应商)" />
        </el-form-item>
        <el-form-item label="生产车间 (加工车间)" prop="productionWorkshop">
          <el-input v-model="form.productionWorkshop" placeholder="请输入生产车间 (加工车间)" />
        </el-form-item>
        <el-form-item label="利润中心" prop="profitCenter">
          <el-input v-model="form.profitCenter" placeholder="请输入利润中心" />
        </el-form-item>
        <el-form-item label="成本中心" prop="costCenter">
          <el-input v-model="form.costCenter" placeholder="请输入成本中心" />
        </el-form-item>
        <el-form-item label="收费备注" prop="chargeRemarks">
          <el-input v-model="form.chargeRemarks" placeholder="请输入收费备注" />
        </el-form-item>
        <el-form-item label="关单原因" prop="closeOrderReason">
          <el-input v-model="form.closeOrderReason" placeholder="请输入关单原因" />
        </el-form-item>
        <el-form-item label="状态1" prop="sta1">
          <el-input v-model="form.sta1" placeholder="请输入状态1" />
        </el-form-item>
        <el-form-item label="SO单 (同步标记)" prop="soDocument">
          <el-input v-model="form.soDocument" placeholder="请输入SO单 (同步标记)" />
        </el-form-item>
        <el-form-item label="MO号 (同步标记)" prop="moDocument">
          <el-input v-model="form.moDocument" placeholder="请输入MO号 (同步标记)" />
        </el-form-item>
        <el-form-item label="PMC分组 (同步标记)" prop="pmcGroup">
          <el-input v-model="form.pmcGroup" placeholder="请输入PMC分组 (同步标记)" />
        </el-form-item>
        <el-form-item label="工艺 (同步标记)" prop="process">
          <el-input v-model="form.process" placeholder="请输入工艺 (同步标记)" />
        </el-form-item>
        <el-form-item label="排产 (同步标记)" prop="productionScheduling">
          <el-input v-model="form.productionScheduling" placeholder="请输入排产 (同步标记)" />
        </el-form-item>
        <el-form-item label="MRP (同步标记)" prop="mrp">
          <el-input v-model="form.mrp" placeholder="请输入MRP (同步标记)" />
        </el-form-item>
        <el-form-item label="请购 (同步标记)" prop="purchaseRequest">
          <el-input v-model="form.purchaseRequest" placeholder="请输入请购 (同步标记)" />
        </el-form-item>
        <el-form-item label="下采购单 (同步标记)" prop="purchaseOrder">
          <el-input v-model="form.purchaseOrder" placeholder="请输入下采购单 (同步标记)" />
        </el-form-item>
        <el-form-item label="材料收货 (同步标记)" prop="materialReceipt">
          <el-input v-model="form.materialReceipt" placeholder="请输入材料收货 (同步标记)" />
        </el-form-item>
        <el-form-item label="IQC检测 (同步标记)" prop="iqcInspection">
          <el-input v-model="form.iqcInspection" placeholder="请输入IQC检测 (同步标记)" />
        </el-form-item>
        <el-form-item label="材料退货 (同步标记)" prop="materialReturn">
          <el-input v-model="form.materialReturn" placeholder="请输入材料退货 (同步标记)" />
        </el-form-item>
        <el-form-item label="材料进仓 (同步标记)" prop="materialInbound">
          <el-input v-model="form.materialInbound" placeholder="请输入材料进仓 (同步标记)" />
        </el-form-item>
        <el-form-item label="托工 (同步标记)" prop="subcontracting">
          <el-input v-model="form.subcontracting" placeholder="请输入托工 (同步标记)" />
        </el-form-item>
        <el-form-item label="请购单号 (同步天心单号)" prop="purchaseRequestNo">
          <el-input v-model="form.purchaseRequestNo" placeholder="请输入请购单号 (同步天心单号)" />
        </el-form-item>
        <el-form-item label="采购单号 (同步天心单号)" prop="purchaseOrderNo">
          <el-input v-model="form.purchaseOrderNo" placeholder="请输入采购单号 (同步天心单号)" />
        </el-form-item>
        <el-form-item label="入库单号 (同步天心单号)" prop="warehouseEntryNo">
          <el-input v-model="form.warehouseEntryNo" placeholder="请输入入库单号 (同步天心单号)" />
        </el-form-item>
        <el-form-item label="出库单号 (同步天心单号)" prop="warehouseExitNo">
          <el-input v-model="form.warehouseExitNo" placeholder="请输入出库单号 (同步天心单号)" />
        </el-form-item>
        <el-form-item label="销货单号 (同步天心单号)" prop="salesOrderNo">
          <el-input v-model="form.salesOrderNo" placeholder="请输入销货单号 (同步天心单号)" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Master" lang="ts">
import { listMaster, getMaster, delMaster, addMaster, updateMaster, submitProcessMethod } from '@/api/tianxin/master';
import { MasterVO, MasterQuery, MasterForm } from '@/api/tianxin/master/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const masterList = ref<MasterVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(false);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
// 添加处理方式的响应式变量
const processMethod = ref('待更新图纸');

const queryFormRef = ref<ElFormInstance>();
const masterFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: MasterForm = {
  id: undefined,
  sta: undefined,
  rem: undefined,
  orderDate: undefined,
  customerReqDeliveryDate: undefined,
  promisedDeliveryDate: undefined,
  nextPlanDate: undefined,
  requiredCompletionDate: undefined,
  lastProcessTime: undefined,
  warehouseEntryDate: undefined,
  actualDeliveryDate: undefined,
  customerCode: undefined,
  projectNo: undefined,
  projectManager: undefined,
  customerPoNo: undefined,
  poItm: undefined,
  soNo: undefined,
  soItm: undefined,
  moNo: undefined,
  parentOrderNo: undefined,
  partClassification: undefined,
  prdNo: undefined,
  dwgNo: undefined,
  version: undefined,
  customerProductNo: undefined,
  customerProductName: undefined,
  productDescription: undefined,
  orderQuantity: undefined,
  productionQuantity: undefined,
  processQuantity: undefined,
  scrapQuantity: undefined,
  inWarehouseCount: undefined,
  outstandingWarehouseCount: undefined,
  deliveryQuantity: undefined,
  outstandingShipmentQuantity: undefined,
  singlePieceNetWeight: undefined,
  unit: undefined,
  industry: undefined,
  orderCategory: undefined,
  planType: undefined,
  orderPlacer: undefined,
  followUpStaff: undefined,
  bomManager: undefined,
  processCompiler: undefined,
  mergeFlag: undefined,
  urgentFlag: undefined,
  sampleFlag: undefined,
  hasStandardParts: undefined,
  isMaterialPurchased: undefined,
  isElectroplated: undefined,
  action: undefined,
  currentProcess: undefined,
  processingMaterial: undefined,
  requiredMaterial: undefined,
  quotedMaterial: undefined,
  drawingDifficultyLevel: undefined,
  processDifficultyLevel: undefined,
  processVersion: undefined,
  completionDepartment: undefined,
  productionWorkshop: undefined,
  profitCenter: undefined,
  costCenter: undefined,
  chargeRemarks: undefined,
  closeOrderReason: undefined,
  sta1: undefined,
  soDocument: undefined,
  moDocument: undefined,
  pmcGroup: undefined,
  process: undefined,
  productionScheduling: undefined,
  mrp: undefined,
  purchaseRequest: undefined,
  purchaseOrder: undefined,
  materialReceipt: undefined,
  iqcInspection: undefined,
  materialReturn: undefined,
  materialInbound: undefined,
  subcontracting: undefined,
  purchaseRequestNo: undefined,
  purchaseOrderNo: undefined,
  warehouseEntryNo: undefined,
  warehouseExitNo: undefined,
  salesOrderNo: undefined
};
const data = reactive<PageData<MasterForm, MasterQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    sta: undefined,
    customerCode: undefined,
    customerPoNo: undefined,
    moNo: undefined,
    dwgNo: undefined,
    finishStatus: '未完成', // 新增的完工状态，默认值为未完成
    params: {}
  },
  rules: {
    id: [{ required: true, message: 'ID不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询PMC订单变更管理列表 */
const getList = async () => {
  loading.value = true;
  const res = await listMaster(queryParams.value);
  masterList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  masterFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: MasterVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加PMC订单变更管理';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: MasterVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getMaster(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改PMC订单变更管理';
};

/** 提交按钮 */
const submitForm = () => {
  masterFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateMaster(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addMaster(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: MasterVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除PMC订单变更管理编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delMaster(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'tianxin/master/export',
    {
      ...queryParams.value
    },
    `master_${new Date().getTime()}.xlsx`
  );
};

/** 处理方式提交操作 */
const handleSubmit = async () => {
  if (masterList.value.length === 0) {
    proxy?.$modal.msgWarning('请先查询数据');
    return;
  }

  try {
    const data = {
      queryParams: queryParams.value,
      processMethod: processMethod.value
    };

    await submitProcessMethod(data);
    proxy?.$modal.msgSuccess('处理方式提交成功');
    getList();
  } catch (error) {
    proxy?.$modal.msgError('处理方式提交失败');
  }
};

onMounted(() => {
  getList();
});
</script>

<style scoped>
/* MO_NO字段样式 */
.mo-no-text {
  font-weight: 500;
  transition: color 0.3s ease;
}

.mo-no-green {
  color: #67c23a !important;
  font-weight: 600;
}
</style>
