<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="PRT_SW" prop="prtSw">
              <el-input v-model="queryParams.prtSw" placeholder="请输入PRT_SW" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="PR号" prop="prNo">
              <el-input v-model="queryParams.prNo" placeholder="请输入PR号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="PR行" prop="prItm">
              <el-input v-model="queryParams.prItm" placeholder="请输入PR行" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="品号" prop="prdNo">
              <el-input v-model="queryParams.prdNo" placeholder="请输入品号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="描述" prop="prdDesc">
              <el-input v-model="queryParams.prdDesc" placeholder="请输入描述" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="数量" prop="qty">
              <el-input v-model="queryParams.qty" placeholder="请输入数量" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="单位" prop="ut">
              <el-input v-model="queryParams.ut" placeholder="请输入单位" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="请求日期" prop="prDate">
              <el-date-picker clearable
                v-model="queryParams.prDate"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择请求日期"
              />
            </el-form-item>
            <el-form-item label="PMC要求日期" prop="pmcRequestDate">
              <el-input v-model="queryParams.pmcRequestDate" placeholder="请输入PMC要求日期" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="厂商代号" prop="vendorCode">
              <el-input v-model="queryParams.vendorCode" placeholder="请输入厂商代号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="简称" prop="vendorSnm">
              <el-input v-model="queryParams.vendorSnm" placeholder="请输入简称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="厂商名" prop="vendorName">
              <el-input v-model="queryParams.vendorName" placeholder="请输入厂商名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="单价" prop="unitPrice">
              <el-input v-model="queryParams.unitPrice" placeholder="请输入单价" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="货币" prop="currency">
              <el-input v-model="queryParams.currency" placeholder="请输入货币" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="是否供料" prop="isProvideMaterials">
              <el-input v-model="queryParams.isProvideMaterials" placeholder="请输入是否供料" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="图号" prop="dwgNo">
              <el-input v-model="queryParams.dwgNo" placeholder="请输入图号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="PJ号" prop="pjNo">
              <el-input v-model="queryParams.pjNo" placeholder="请输入PJ号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="MO号" prop="moNo">
              <el-input v-model="queryParams.moNo" placeholder="请输入MO号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="订单号" prop="purchaseNum">
              <el-input v-model="queryParams.purchaseNum" placeholder="请输入订单号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="批号" prop="batchNo">
              <el-input v-model="queryParams.batchNo" placeholder="请输入批号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="总价" prop="sumPrice">
              <el-input v-model="queryParams.sumPrice" placeholder="请输入总价" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="状态" prop="sta">
              <el-input v-model="queryParams.sta" placeholder="请输入状态" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="外发交期" prop="outsourcingDate">
              <el-date-picker clearable
                v-model="queryParams.outsourcingDate"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择外发交期"
              />
            </el-form-item>
            <el-form-item label="客户代码" prop="custNo">
              <el-input v-model="queryParams.custNo" placeholder="请输入客户代码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="工序号" prop="zcNo">
              <el-input v-model="queryParams.zcNo" placeholder="请输入工序号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="加工中心" prop="machiningCenter">
              <el-input v-model="queryParams.machiningCenter" placeholder="请输入加工中心" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="工序名称" prop="zcName">
              <el-input v-model="queryParams.zcName" placeholder="请输入工序名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="电镀厂商" prop="epfSnm">
              <el-input v-model="queryParams.epfSnm" placeholder="请输入电镀厂商" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="指定材料" prop="specifiedMaterials">
              <el-input v-model="queryParams.specifiedMaterials" placeholder="请输入指定材料" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="是否电镀" prop="isElectroplate">
              <el-input v-model="queryParams.isElectroplate" placeholder="请输入是否电镀" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="电镀厂商代号" prop="epfCode">
              <el-input v-model="queryParams.epfCode" placeholder="请输入电镀厂商代号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="电镀厂商名" prop="epfName">
              <el-input v-model="queryParams.epfName" placeholder="请输入电镀厂商名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="USR" prop="usr">
              <el-input v-model="queryParams.usr" placeholder="请输入USR" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="SYS_DATE" prop="sysDate">
              <el-date-picker clearable
                v-model="queryParams.sysDate"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择SYS_DATE"
              />
            </el-form-item>
            <el-form-item label="host" prop="host">
              <el-input v-model="queryParams.host" placeholder="请输入host" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="备注" prop="rem">
              <el-input v-model="queryParams.rem" placeholder="请输入备注" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['tianxin:processLab:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['tianxin:processLab:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['tianxin:processLab:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['tianxin:processLab:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="processLabList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="主键" align="center" prop="id" v-if="true" />
        <el-table-column label="PRT_SW" align="center" prop="prtSw" />
        <el-table-column label="PR号" align="center" prop="prNo" />
        <el-table-column label="PR行" align="center" prop="prItm" />
        <el-table-column label="申请类型" align="center" prop="prType" />
        <el-table-column label="品号" align="center" prop="prdNo" />
        <el-table-column label="描述" align="center" prop="prdDesc" />
        <el-table-column label="数量" align="center" prop="qty" />
        <el-table-column label="单位" align="center" prop="ut" />
        <el-table-column label="请求日期" align="center" prop="prDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.prDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="PMC要求日期" align="center" prop="pmcRequestDate" />
        <el-table-column label="厂商代号" align="center" prop="vendorCode" />
        <el-table-column label="简称" align="center" prop="vendorSnm" />
        <el-table-column label="厂商名" align="center" prop="vendorName" />
        <el-table-column label="单价" align="center" prop="unitPrice" />
        <el-table-column label="货币" align="center" prop="currency" />
        <el-table-column label="是否供料" align="center" prop="isProvideMaterials" />
        <el-table-column label="图号" align="center" prop="dwgNo" />
        <el-table-column label="PJ号" align="center" prop="pjNo" />
        <el-table-column label="MO号" align="center" prop="moNo" />
        <el-table-column label="订单号" align="center" prop="purchaseNum" />
        <el-table-column label="批号" align="center" prop="batchNo" />
        <el-table-column label="总价" align="center" prop="sumPrice" />
        <el-table-column label="状态" align="center" prop="sta" />
        <el-table-column label="外发交期" align="center" prop="outsourcingDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.outsourcingDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="客户代码" align="center" prop="custNo" />
        <el-table-column label="工序号" align="center" prop="zcNo" />
        <el-table-column label="加工中心" align="center" prop="machiningCenter" />
        <el-table-column label="工序名称" align="center" prop="zcName" />
        <el-table-column label="电镀厂商" align="center" prop="epfSnm" />
        <el-table-column label="电镀内容" align="center" prop="electroplateContent" />
        <el-table-column label="指定材料" align="center" prop="specifiedMaterials" />
        <el-table-column label="是否电镀" align="center" prop="isElectroplate" />
        <el-table-column label="电镀厂商代号" align="center" prop="epfCode" />
        <el-table-column label="电镀厂商名" align="center" prop="epfName" />
        <el-table-column label="USR" align="center" prop="usr" />
        <el-table-column label="SYS_DATE" align="center" prop="sysDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.sysDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="host" align="center" prop="host" />
        <el-table-column label="备注" align="center" prop="rem" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['tianxin:processLab:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['tianxin:processLab:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="processLabFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="PRT_SW" prop="prtSw">
          <el-input v-model="form.prtSw" placeholder="请输入PRT_SW" />
        </el-form-item>
        <el-form-item label="PR号" prop="prNo">
          <el-input v-model="form.prNo" placeholder="请输入PR号" />
        </el-form-item>
        <el-form-item label="PR行" prop="prItm">
          <el-input v-model="form.prItm" placeholder="请输入PR行" />
        </el-form-item>
        <el-form-item label="品号" prop="prdNo">
          <el-input v-model="form.prdNo" placeholder="请输入品号" />
        </el-form-item>
        <el-form-item label="描述" prop="prdDesc">
          <el-input v-model="form.prdDesc" placeholder="请输入描述" />
        </el-form-item>
        <el-form-item label="数量" prop="qty">
          <el-input v-model="form.qty" placeholder="请输入数量" />
        </el-form-item>
        <el-form-item label="单位" prop="ut">
          <el-input v-model="form.ut" placeholder="请输入单位" />
        </el-form-item>
        <el-form-item label="请求日期" prop="prDate">
          <el-date-picker clearable
            v-model="form.prDate"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择请求日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="PMC要求日期" prop="pmcRequestDate">
          <el-input v-model="form.pmcRequestDate" placeholder="请输入PMC要求日期" />
        </el-form-item>
        <el-form-item label="厂商代号" prop="vendorCode">
          <el-input v-model="form.vendorCode" placeholder="请输入厂商代号" />
        </el-form-item>
        <el-form-item label="简称" prop="vendorSnm">
          <el-input v-model="form.vendorSnm" placeholder="请输入简称" />
        </el-form-item>
        <el-form-item label="厂商名" prop="vendorName">
          <el-input v-model="form.vendorName" placeholder="请输入厂商名" />
        </el-form-item>
        <el-form-item label="单价" prop="unitPrice">
          <el-input v-model="form.unitPrice" placeholder="请输入单价" />
        </el-form-item>
        <el-form-item label="货币" prop="currency">
          <el-input v-model="form.currency" placeholder="请输入货币" />
        </el-form-item>
        <el-form-item label="是否供料" prop="isProvideMaterials">
          <el-input v-model="form.isProvideMaterials" placeholder="请输入是否供料" />
        </el-form-item>
        <el-form-item label="图号" prop="dwgNo">
          <el-input v-model="form.dwgNo" placeholder="请输入图号" />
        </el-form-item>
        <el-form-item label="PJ号" prop="pjNo">
          <el-input v-model="form.pjNo" placeholder="请输入PJ号" />
        </el-form-item>
        <el-form-item label="MO号" prop="moNo">
          <el-input v-model="form.moNo" placeholder="请输入MO号" />
        </el-form-item>
        <el-form-item label="订单号" prop="purchaseNum">
          <el-input v-model="form.purchaseNum" placeholder="请输入订单号" />
        </el-form-item>
        <el-form-item label="批号" prop="batchNo">
          <el-input v-model="form.batchNo" placeholder="请输入批号" />
        </el-form-item>
        <el-form-item label="总价" prop="sumPrice">
          <el-input v-model="form.sumPrice" placeholder="请输入总价" />
        </el-form-item>
        <el-form-item label="状态" prop="sta">
          <el-input v-model="form.sta" placeholder="请输入状态" />
        </el-form-item>
        <el-form-item label="外发交期" prop="outsourcingDate">
          <el-date-picker clearable
            v-model="form.outsourcingDate"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择外发交期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="客户代码" prop="custNo">
          <el-input v-model="form.custNo" placeholder="请输入客户代码" />
        </el-form-item>
        <el-form-item label="工序号" prop="zcNo">
          <el-input v-model="form.zcNo" placeholder="请输入工序号" />
        </el-form-item>
        <el-form-item label="加工中心" prop="machiningCenter">
          <el-input v-model="form.machiningCenter" placeholder="请输入加工中心" />
        </el-form-item>
        <el-form-item label="工序名称" prop="zcName">
          <el-input v-model="form.zcName" placeholder="请输入工序名称" />
        </el-form-item>
        <el-form-item label="电镀厂商" prop="epfSnm">
          <el-input v-model="form.epfSnm" placeholder="请输入电镀厂商" />
        </el-form-item>
        <el-form-item label="电镀内容">
          <editor v-model="form.electroplateContent" :min-height="192"/>
        </el-form-item>
        <el-form-item label="指定材料" prop="specifiedMaterials">
          <el-input v-model="form.specifiedMaterials" placeholder="请输入指定材料" />
        </el-form-item>
        <el-form-item label="是否电镀" prop="isElectroplate">
          <el-input v-model="form.isElectroplate" placeholder="请输入是否电镀" />
        </el-form-item>
        <el-form-item label="电镀厂商代号" prop="epfCode">
          <el-input v-model="form.epfCode" placeholder="请输入电镀厂商代号" />
        </el-form-item>
        <el-form-item label="电镀厂商名" prop="epfName">
          <el-input v-model="form.epfName" placeholder="请输入电镀厂商名" />
        </el-form-item>
        <el-form-item label="USR" prop="usr">
          <el-input v-model="form.usr" placeholder="请输入USR" />
        </el-form-item>
        <el-form-item label="SYS_DATE" prop="sysDate">
          <el-date-picker clearable
            v-model="form.sysDate"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择SYS_DATE">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="host" prop="host">
          <el-input v-model="form.host" placeholder="请输入host" />
        </el-form-item>
        <el-form-item label="备注" prop="rem">
          <el-input v-model="form.rem" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ProcessLab" lang="ts">
import { listProcessLab, getProcessLab, delProcessLab, addProcessLab, updateProcessLab } from '@/api/tianxin/processLab';
import { ProcessLabVO, ProcessLabQuery, ProcessLabForm } from '@/api/tianxin/processLab/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const processLabList = ref<ProcessLabVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const processLabFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: ProcessLabForm = {
  id: undefined,
  prtSw: undefined,
  prNo: undefined,
  prItm: undefined,
  prType: undefined,
  prdNo: undefined,
  prdDesc: undefined,
  qty: undefined,
  ut: undefined,
  prDate: undefined,
  pmcRequestDate: undefined,
  vendorCode: undefined,
  vendorSnm: undefined,
  vendorName: undefined,
  unitPrice: undefined,
  currency: undefined,
  isProvideMaterials: undefined,
  dwgNo: undefined,
  pjNo: undefined,
  moNo: undefined,
  purchaseNum: undefined,
  batchNo: undefined,
  sumPrice: undefined,
  sta: undefined,
  outsourcingDate: undefined,
  custNo: undefined,
  zcNo: undefined,
  machiningCenter: undefined,
  zcName: undefined,
  epfSnm: undefined,
  electroplateContent: undefined,
  specifiedMaterials: undefined,
  isElectroplate: undefined,
  epfCode: undefined,
  epfName: undefined,
  usr: undefined,
  sysDate: undefined,
  host: undefined,
  rem: undefined,
}
const data = reactive<PageData<ProcessLabForm, ProcessLabQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    prtSw: undefined,
    prNo: undefined,
    prItm: undefined,
    prType: undefined,
    prdNo: undefined,
    prdDesc: undefined,
    qty: undefined,
    ut: undefined,
    prDate: undefined,
    pmcRequestDate: undefined,
    vendorCode: undefined,
    vendorSnm: undefined,
    vendorName: undefined,
    unitPrice: undefined,
    currency: undefined,
    isProvideMaterials: undefined,
    dwgNo: undefined,
    pjNo: undefined,
    moNo: undefined,
    purchaseNum: undefined,
    batchNo: undefined,
    sumPrice: undefined,
    sta: undefined,
    outsourcingDate: undefined,
    custNo: undefined,
    zcNo: undefined,
    machiningCenter: undefined,
    zcName: undefined,
    epfSnm: undefined,
    electroplateContent: undefined,
    specifiedMaterials: undefined,
    isElectroplate: undefined,
    epfCode: undefined,
    epfName: undefined,
    usr: undefined,
    sysDate: undefined,
    host: undefined,
    rem: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "主键不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等列表 */
const getList = async () => {
  loading.value = true;
  const res = await listProcessLab(queryParams.value);
  processLabList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  processLabFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: ProcessLabVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: ProcessLabVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getProcessLab(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等";
}

/** 提交按钮 */
const submitForm = () => {
  processLabFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateProcessLab(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addProcessLab(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: ProcessLabVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delProcessLab(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('tianxin/processLab/export', {
    ...queryParams.value
  }, `processLab_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
