<template>
  <div class="pdf-barcode-container">
    <div class="main-content">
      <el-card class="main-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <div class="header-icon">
              <el-icon size="24" color="#409EFF">
                <Document />
              </el-icon>
            </div>
            <div class="header-text">
              <h2 class="title">PDF条形码生成</h2>
              <p class="subtitle">批量处理PDF文件，自动添加条形码</p>
            </div>
          </div>
        </template>

        <el-alert
          type="info"
          :closable="false"
          show-icon
          class="info-alert"
          title="支持一次上传最多30个PDF文件，单个文件不超过50MB。系统将自动为每个PDF文件生成条形码并添加到指定位置。"
        />

        <el-form ref="uploadFormRef" :model="uploadForm" :rules="uploadRules" label-width="100px" class="upload-form">
          <el-form-item label="选择PDF" prop="files" class="upload-item">
            <el-upload
              ref="uploadRef"
              drag
              multiple
              action="#"
              :auto-upload="false"
              :file-list="uploadFileList"
              :limit="50"
              :accept="fileAccept"
              :before-upload="handleBeforeUpload"
              :on-exceed="handleExceed"
              :on-change="handleFileChange"
              :on-remove="handleFileRemove"
              class="upload-dragger"
            >
              <div class="upload-content">
                <el-icon class="upload-icon" size="48" color="#C0C4CC">
                  <UploadFilled />
                </el-icon>
                <div class="upload-text">将PDF文件拖到此处，或点击选择文件</div>
                <div class="upload-hint">支持批量上传，最多50个文件</div>
              </div>
              <template #tip>
                <div class="upload-tip">
                  <el-icon><Document /></el-icon>
                  仅支持 .pdf 文件，单个文件不超过50MB
                </div>
              </template>
            </el-upload>
          </el-form-item>

          <el-form-item label="条形码位置" prop="barcodePosition" class="position-item">
            <div class="position-config">
              <!-- 位置选择器 -->
              <div class="position-selector">
                <!--                <label class="position-label">位置选择</label>-->
                <div class="position-grid">
                  <div
                    v-for="position in positionOptions"
                    :key="position.value"
                    :class="['position-option', { active: uploadForm.barcodeHorizontal === position.value }]"
                    @click="selectPosition(position.value)"
                  >
                    <div class="position-icon">
                      <el-icon><component :is="position.icon" /></el-icon>
                    </div>
                    <div class="position-name">{{ position.label }}</div>
                    <div class="position-desc">{{ position.desc }}</div>
                  </div>
                </div>
              </div>

              <!-- 边距配置 -->
              <div class="margin-config">
                <div class="margin-section">
                  <h4 class="margin-title">边距设置</h4>
                  <div class="margin-item">
                    <label class="margin-label">
                      <el-icon><ArrowRight /></el-icon>
                      水平边距
                    </label>
                    <el-input-number
                      v-model="uploadForm.barcodeX"
                      :min="0"
                      :max="1000"
                      :step="5"
                      placeholder="边距像素"
                      class="margin-input"
                      controls-position="right"
                    />
                    <span class="margin-unit">px</span>
                  </div>
                  <div class="margin-item">
                    <label class="margin-label">
                      <el-icon><ArrowDown /></el-icon>
                      垂直边距
                    </label>
                    <el-input-number
                      v-model="uploadForm.barcodeY"
                      :min="0"
                      :max="1000"
                      :step="5"
                      placeholder="垂直边距像素"
                      class="margin-input"
                      controls-position="right"
                    />
                    <span class="margin-unit">px</span>
                  </div>
                </div>

                <!-- 预览区域 -->
                <div class="preview-section">
                  <h4 class="preview-title">位置预览</h4>
                  <div class="preview-container">
                    <div class="preview-page">
                      <div :class="['preview-barcode', getPreviewPosition()]" :style="getPreviewStyle()"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="position-hint">
              <el-icon><InfoFilled /></el-icon>
              点击选择条形码位置，调整边距参数，右侧可预览效果
            </div>
          </el-form-item>

          <el-form-item label="条形码尺寸" prop="barcodeSize" class="size-item">
            <div class="size-config">
              <div class="size-item-wrapper">
                <label class="size-label">宽度</label>
                <el-input-number
                  v-model="uploadForm.barcodeWidth"
                  :min="50"
                  :max="500"
                  :step="10"
                  placeholder="条形码宽度"
                  class="size-input"
                  controls-position="right"
                />
                <span class="size-unit">px</span>
              </div>
              <div class="size-item-wrapper">
                <label class="size-label">高度</label>
                <el-input-number
                  v-model="uploadForm.barcodeHeight"
                  :min="20"
                  :max="200"
                  :step="5"
                  placeholder="条形码高度"
                  class="size-input"
                  controls-position="right"
                />
                <span class="size-unit">px</span>
              </div>
            </div>
            <div class="size-hint">
              <el-icon><InfoFilled /></el-icon>
              设置条形码的宽度和高度（像素）
            </div>
          </el-form-item>

          <!-- 文件状态显示 -->
          <el-form-item class="file-status-item" v-if="uploadFileList.length > 0">
            <div class="file-status">
              <el-icon class="status-icon"><Document /></el-icon>
              <span class="file-count">已选择文件: {{ uploadFileList.length }} 个</span>
              <span class="file-size-info"> ({{ formatFileSize() }}) </span>
            </div>
          </el-form-item>

          <el-form-item class="action-item">
            <div class="action-buttons">
              <el-button
                :loading="uploadLoading"
                type="primary"
                size="large"
                @click="submitUploadGenerate"
                :disabled="uploadFileList.length === 0"
                class="generate-btn"
              >
                <el-icon><Download /></el-icon>
                生成条形码并下载
              </el-button>
              <el-button @click="resetUpload" :disabled="uploadLoading" size="large" class="clear-btn">
                <el-icon><Delete /></el-icon>
                清空
              </el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script setup name="PdfBarcode" lang="ts">
import { ComponentInternalInstance, getCurrentInstance, nextTick, reactive, ref, toRefs } from 'vue';
import { ElMessage } from 'element-plus';
import { Document, UploadFilled, InfoFilled, Download, Delete, ArrowRight, ArrowDown } from '@element-plus/icons-vue';
import { globalHeaders } from '@/utils/request';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

type UploadForm = {
  files: any[];
  barcodeHorizontal: string;
  barcodeX: number;
  barcodeY: number;
  barcodeWidth: number;
  barcodeHeight: number;
};

const fileAccept = '.pdf';
const uploadLoading = ref(false);
const uploadFormRef = ref<ElFormInstance>();
const uploadRef = ref();
const uploadFileList = ref<any[]>([]);

// 位置选项配置
const positionOptions = ref([
  { value: 'left-top', label: '左上角', desc: '距离左上角', icon: 'TopLeft' },
  { value: 'left-center', label: '左中', desc: '左侧垂直居中', icon: 'Left' },
  { value: 'left-bottom', label: '左下角', desc: '距离左下角', icon: 'BottomLeft' },
  { value: 'center-top', label: '中上', desc: '顶部水平居中', icon: 'Top' },
  { value: 'center-center', label: '正中心', desc: '页面正中心', icon: 'CircleCheck' },
  { value: 'center-bottom', label: '中下', desc: '底部水平居中', icon: 'Bottom' },
  { value: 'right-top', label: '右上角', desc: '距离右上角', icon: 'TopRight' },
  { value: 'right-center', label: '右中', desc: '右侧垂直居中', icon: 'Right' },
  { value: 'right-bottom', label: '右下角', desc: '距离右下角', icon: 'BottomRight' }
]);

const initUploadFormData: UploadForm = {
  files: [],
  barcodeHorizontal: 'right-top', // 默认放在右上角
  barcodeX: 20, // 距离边缘20px
  barcodeY: 20, // 距离边缘20px
  barcodeWidth: 120, // 条形码宽度
  barcodeHeight: 30 // 条形码高度
};

const uploadData = reactive<{ form: UploadForm; rules: ElFormRules }>({
  form: { ...initUploadFormData },
  rules: {
    files: [
      {
        validator: (rule: any, value: any, callback: any) => {
          const list = uploadFileList.value;
          if (!list || (Array.isArray(list) && list.length === 0)) {
            callback(new Error('请至少上传1个PDF文件'));
          } else if (Array.isArray(list) && list.length > 50) {
            callback(new Error('一次最多上传50个PDF文件'));
          } else {
            callback();
          }
        },
        trigger: 'change'
      }
    ],
    barcodeHorizontal: [{ required: true, message: '请选择水平位置', trigger: 'change' }],
    barcodeX: [
      { required: true, message: '边距不能为空', trigger: 'blur' },
      { type: 'number' as const, min: 0, max: 1000, message: '边距必须在0-1000之间', trigger: 'blur' }
    ],
    barcodeY: [
      { required: true, message: 'Y坐标不能为空', trigger: 'blur' },
      { type: 'number' as const, min: 0, max: 1000, message: 'Y坐标必须在0-1000之间', trigger: 'blur' }
    ],
    barcodeWidth: [
      { required: true, message: '宽度不能为空', trigger: 'blur' },
      { type: 'number' as const, min: 50, max: 500, message: '宽度必须在50-500之间', trigger: 'blur' }
    ],
    barcodeHeight: [
      { required: true, message: '高度不能为空', trigger: 'blur' },
      { type: 'number' as const, min: 20, max: 200, message: '高度必须在20-200之间', trigger: 'blur' }
    ]
  }
});

const { form: uploadForm, rules: uploadRules } = toRefs(uploadData);

// 方法
const handleBeforeUpload = (file: File) => {
  // 验证文件类型
  if (!file.name.toLowerCase().endsWith('.pdf')) {
    ElMessage.error('只支持PDF文件');
    return false;
  }

  // 验证文件大小 (50MB)
  const maxSize = 50 * 1024 * 1024;
  if (file.size > maxSize) {
    ElMessage.error(`文件 ${file.name} 过大，最大支持50MB`);
    return false;
  }

  return false; // 阻止自动上传
};

const handleExceed = (files: File[]) => {
  ElMessage.warning(`最多只能选择50个文件，当前已选择${uploadFileList.value.length}个`);
};

const handleFileChange = (file: any, fileList: any[]) => {
  // 更新文件状态
  file.status = 'ready';
  uploadForm.value.files = fileList;
  // 确保 uploadFileList 也同步更新
  uploadFileList.value = fileList;

  console.log('文件变化:', {
    fileName: file.name,
    fileListLength: fileList.length,
    uploadFileListLength: uploadFileList.value.length
  });
};

const handleFileRemove = (file: any, fileList: any[]) => {
  uploadForm.value.files = fileList;
  // 确保 uploadFileList 也同步更新
  uploadFileList.value = fileList;
};

const submitUploadGenerate = async () => {
  try {
    // 验证表单
    await uploadFormRef.value?.validate();

    if (uploadFileList.value.length === 0) {
      ElMessage.warning('请选择要上传的PDF文件');
      return;
    }

    uploadLoading.value = true;

    // 使用fetch API直接调用后端，确保正确处理multipart请求
    try {
      console.log('开始调用API...');

      const formData = new FormData();
      uploadFileList.value.forEach((f: any) => {
        if (f.raw) formData.append('files', f.raw);
      });

      // 添加条形码位置和尺寸参数
      formData.append('barcodeHorizontal', uploadForm.value.barcodeHorizontal);
      formData.append('barcodeX', String(uploadForm.value.barcodeX));
      formData.append('barcodeY', String(uploadForm.value.barcodeY));
      formData.append('barcodeWidth', String(uploadForm.value.barcodeWidth));
      formData.append('barcodeHeight', String(uploadForm.value.barcodeHeight));

      const baseURL = import.meta.env.VITE_APP_BASE_API;
      const headers = globalHeaders();

      console.log('请求URL:', `${baseURL}/tianxin/pdfBarcode/upload`);
      console.log('请求头:', headers);
      console.log('FormData内容:', formData);

      const resp = await fetch(`${baseURL}/tianxin/pdfBarcode/upload`, {
        method: 'POST',
        headers: { ...headers }, // 不设置Content-Type，让浏览器自动设置multipart边界
        body: formData
      });

      console.log('响应状态:', resp.status);
      console.log('响应状态文本:', resp.statusText);
      console.log('响应头信息:', resp.headers);
      console.log('响应类型:', resp.type);

      const blob = await resp.blob();
      console.log('Blob数据:', blob);
      console.log('Blob类型:', blob.type);
      console.log('Blob大小:', blob.size);

      // 检查响应是否成功且包含ZIP数据
      const isOk = resp.ok && blob && blob.type !== 'application/json';
      console.log('响应检查结果:', { isOk, respOk: resp.ok, blobExists: !!blob, blobType: blob.type });

      if (isOk) {
        // 成功返回ZIP包
        console.log('检测到ZIP包，开始下载');
        const fileName = `pdf_barcode_${new Date().getTime()}.zip`;
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        a.remove();

        ElMessage.success('PDF条形码生成成功，正在下载...');
        resetUpload();
      } else {
        // 处理失败，尝试解析错误信息
        console.log('未检测到ZIP包，尝试解析错误信息');
        const text = await blob.text();
        console.log('错误响应文本:', text);

        try {
          const obj = JSON.parse(text);
          throw new Error(obj.message || '处理失败，请重试');
        } catch (parseError) {
          console.error('解析错误响应失败:', parseError);
          throw new Error('处理失败，请重试');
        }
      }
    } catch (apiError: any) {
      console.error('API调用失败:', apiError);
      throw new Error(apiError.message || '处理失败，请重试');
    }
  } catch (error: any) {
    ElMessage.error('文件上传失败: ' + (error.response?.data?.msg || error.message || error));
  } finally {
    uploadLoading.value = false;
  }
};

// 选择位置
const selectPosition = (position: string) => {
  uploadForm.value.barcodeHorizontal = position;
};

// 获取预览位置类名
const getPreviewPosition = () => {
  return uploadForm.value.barcodeHorizontal || 'right-top';
};

// 获取预览样式
const getPreviewStyle = () => {
  const width = Math.max(20, uploadForm.value.barcodeWidth / 4);
  const height = Math.max(8, uploadForm.value.barcodeHeight / 4);
  return {
    width: `${width}px`,
    height: `${height}px`
  };
};

// 格式化文件大小
const formatFileSize = () => {
  if (uploadFileList.value.length === 0) return '';

  let totalSize = 0;
  uploadFileList.value.forEach((file) => {
    if (file.raw) {
      totalSize += file.raw.size;
    }
  });

  if (totalSize < 1024) {
    return `${totalSize} B`;
  } else if (totalSize < 1024 * 1024) {
    return `${(totalSize / 1024).toFixed(1)} KB`;
  } else {
    return `${(totalSize / (1024 * 1024)).toFixed(1)} MB`;
  }
};

const resetUpload = () => {
  uploadFileList.value = [];
  uploadForm.value = { ...initUploadFormData };
  uploadFormRef.value?.resetFields();
  uploadRef.value?.clearFiles?.();
  nextTick(() => {
    uploadFormRef.value?.clearValidate();
  });
};
</script>

<style scoped>
/* 主容器样式 */
.pdf-barcode-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.main-content {
  max-width: 1000px;
  margin: 0 auto;
}

/* 卡片样式 */
.main-card {
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: none;
  overflow: hidden;
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 0;
}

.header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #409eff, #67c23a);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.header-text .title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1.2;
}

.header-text .subtitle {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: #909399;
  line-height: 1.4;
}

/* 信息提示框 */
.info-alert {
  margin: 20px 0;
  border-radius: 12px;
  border: none;
  background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
}

/* 表单样式 */
.upload-form {
  padding: 20px 0;
}

.upload-item {
  margin-bottom: 30px;
}

/* 上传区域样式 */
.upload-dragger {
  width: 100%;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.upload-icon {
  margin-bottom: 16px;
  opacity: 0.8;
}

.upload-text {
  font-size: 16px;
  color: #606266;
  margin-bottom: 8px;
  font-weight: 500;
}

.upload-hint {
  font-size: 14px;
  color: #909399;
}

.upload-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  margin-top: 12px;
  font-size: 13px;
  color: #909399;
}

/* 位置配置样式 */
.position-item {
  margin-bottom: 30px;
}

.position-config {
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

.position-selector {
  flex: 1;
}

.position-label {
  display: block;
  margin-bottom: 16px;
  font-weight: 500;
  color: #303133;
  font-size: 16px;
}

/* 位置网格 */
.position-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.position-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 12px;
  border: 2px solid #e4e7ed;
  border-radius: 12px;
  background: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.position-option:hover {
  border-color: #409eff;
  background: #f0f9ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.position-option.active {
  border-color: #409eff;
  background: linear-gradient(135deg, #409eff, #67c23a);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.3);
}

.position-icon {
  margin-bottom: 8px;
  font-size: 20px;
}

.position-option.active .position-icon {
  color: white;
}

.position-name {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
}

.position-desc {
  font-size: 12px;
  opacity: 0.8;
}

/* 边距配置 */
.margin-config {
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-width: 280px;
}

.margin-section {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.margin-title {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.margin-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.margin-item:last-child {
  margin-bottom: 0;
}

.margin-label {
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 90px;
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.margin-input {
  flex: 1;
}

.margin-unit {
  font-size: 14px;
  color: #909399;
  min-width: 20px;
}

/* 预览区域 */
.preview-section {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.preview-title {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-page {
  position: relative;
  width: 200px;
  height: 280px;
  background: white;
  border: 2px solid #dcdfe6;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-barcode {
  position: absolute;
  background: linear-gradient(45deg, #409eff, #67c23a);
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 预览位置类 */
.preview-barcode.left-top {
  top: 20px;
  left: 20px;
}

.preview-barcode.left-center {
  top: 50%;
  left: 20px;
  transform: translateY(-50%);
}

.preview-barcode.left-bottom {
  bottom: 20px;
  left: 20px;
}

.preview-barcode.center-top {
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
}

.preview-barcode.center-center {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.preview-barcode.center-bottom {
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
}

.preview-barcode.right-top {
  top: 20px;
  right: 20px;
}

.preview-barcode.right-center {
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
}

.preview-barcode.right-bottom {
  bottom: 20px;
  right: 20px;
}

.position-hint {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 16px;
  font-size: 13px;
  color: #909399;
  padding: 12px 16px;
  background: #f0f9ff;
  border-radius: 8px;
  border: 1px solid #b3d8ff;
}

/* 尺寸配置样式 */
.size-item {
  margin-bottom: 30px;
}

.size-config {
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

.size-item-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.size-label {
  min-width: 60px;
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.size-input {
  flex: 1;
}

.size-unit {
  font-size: 14px;
  color: #909399;
  min-width: 20px;
}

.size-hint {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 12px;
  font-size: 13px;
  color: #909399;
}

/* 文件状态样式 */
.file-status-item {
  margin-top: 20px;
  margin-bottom: 0;
}

.file-status {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #606266;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f0f9ff, #ffffff);
  border-radius: 8px;
  border: 1px solid #bfdbfe;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
  transition: all 0.3s ease;
}

.file-status:hover {
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  transform: translateY(-1px);
}

.status-icon {
  color: #3b82f6;
  font-size: 16px;
}

.file-count {
  font-weight: 500;
  color: #1e40af;
}

.file-size-info {
  font-size: 12px;
  color: #64748b;
  margin-left: 4px;
}

/* 操作按钮样式 */
.action-item {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.action-buttons {
  display: flex;
  gap: 16px;
}

.generate-btn {
  background: linear-gradient(135deg, #409eff, #67c23a);
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
}

.generate-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
}

.clear-btn {
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.clear-btn:hover {
  transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pdf-barcode-container {
    padding: 10px;
  }

  .position-config {
    flex-direction: column;
    gap: 16px;
  }

  .position-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .position-option {
    padding: 12px 8px;
  }

  .margin-config {
    min-width: auto;
  }

  .preview-page {
    width: 150px;
    height: 210px;
  }

  .size-config {
    flex-direction: column;
    gap: 16px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 12px;
  }

  .file-status-item {
    margin-top: 16px;
  }

  .header-text .title {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .position-grid {
    grid-template-columns: 1fr;
  }

  .preview-page {
    width: 120px;
    height: 168px;
  }
}

/* Element Plus 组件样式覆盖 */
:deep(.el-upload-dragger) {
  width: 100%;
  height: 200px;
  border: 2px dashed #d9d9d9;
  border-radius: 12px;
  background: #fafafa;
  transition: all 0.3s ease;
}

:deep(.el-upload-dragger:hover) {
  border-color: #409eff;
  background: #f0f9ff;
}

:deep(.el-upload-list) {
  margin-top: 16px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #303133;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
}

:deep(.el-card__header) {
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  border-bottom: 1px solid #e9ecef;
  padding: 20px 24px;
}

:deep(.el-card__body) {
  padding: 24px;
}
</style>
