<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <!--            <el-form-item label="动作" prop="action">
              <el-select v-model="queryParams.action" placeholder="请选择动作" clearable filterable>
                <el-option
                  v-for="action in organizationActions"
                  :key="action.action"
                  :label="`${action.action} (${action.department})`"
                  :value="action.action"
                >
                  <span style="float: left">{{ action.action }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ action.department }}</span>
                </el-option>
              </el-select>
            </el-form-item>-->
            <el-form-item label="动作" prop="action">
              <el-input v-model="queryParams.action" placeholder="请输入动作名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="操作人工号" prop="operatorCode">
              <el-input v-model="queryParams.operatorCode" placeholder="请输入操作人工号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="姓名" prop="operatorName">
              <el-input v-model="queryParams.operatorName" placeholder="请输入姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="操作时间" prop="operationTime">
              <el-date-picker clearable v-model="queryParams.operationTime" type="date" value-format="YYYY-MM-DD" placeholder="请选择操作时间" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['tianxin:actionInfo:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['tianxin:actionInfo:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['tianxin:actionInfo:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['tianxin:actionInfo:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="actionInfoList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="主键" align="center" prop="id" v-if="false" />
        <el-table-column label="动作" align="center" prop="action">
          <template #default="scope">
            <dict-tag :options="is_use" :value="scope.row.action" />
          </template>
        </el-table-column>
        <el-table-column label="部门" align="center" prop="department" />
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="sys_common_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="序号" align="center" prop="sequenceNo" />
        <el-table-column label="条码号" align="center" prop="barcodeNo" />
        <el-table-column label="是否控制数量" align="center" prop="isQuantityControlled">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.isQuantityControlled" />
          </template>
        </el-table-column>
        <el-table-column label="是否输入通过数量" align="center" prop="isPassedQuantity">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.isPassedQuantity" />
          </template>
        </el-table-column>
        <el-table-column label="是否输入报废数量" align="center" prop="isScrappedQuantity">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.isScrappedQuantity" />
          </template>
        </el-table-column>
        <el-table-column label="是否自带上工序数量" align="center" prop="isPreviousProcess">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.isPreviousProcess" />
          </template>
        </el-table-column>
        <el-table-column label="是否跳工序" align="center" prop="allowSkipProcess">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.allowSkipProcess" />
          </template>
        </el-table-column>
        <el-table-column label="是否输入数量" align="center" prop="noQuantity">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.noQuantity" />
          </template>
        </el-table-column>
        <el-table-column label="操作人工号" align="center" prop="operatorCode" />
        <el-table-column label="姓名" align="center" prop="operatorName" />
        <el-table-column label="操作时间" align="center" prop="operationTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.operationTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['tianxin:actionInfo:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['tianxin:actionInfo:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改机床扫描查询对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="actionInfoFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="动作" prop="action">
          <el-select v-model="form.action" placeholder="请选择动作">
            <el-option v-for="dict in is_use" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="部门" prop="department">
          <el-input v-model="form.department" placeholder="请输入部门" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择状态">
            <el-option v-for="dict in sys_common_status" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="序号" prop="sequenceNo">
          <el-input v-model="form.sequenceNo" placeholder="请输入序号" />
        </el-form-item>
        <el-form-item label="条码号" prop="barcodeNo">
          <el-input v-model="form.barcodeNo" placeholder="请输入条码号" />
        </el-form-item>
        <el-form-item label="是否控制数量" prop="isQuantityControlled">
          <el-select v-model="form.isQuantityControlled" placeholder="请选择是否控制数量">
            <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否输入通过数量" prop="isPassedQuantity">
          <el-select v-model="form.isPassedQuantity" placeholder="请选择是否输入通过数量">
            <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否输入报废数量" prop="isScrappedQuantity">
          <el-select v-model="form.isScrappedQuantity" placeholder="请选择是否输入报废数量">
            <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否自带上工序数量" prop="isPreviousProcess">
          <el-select v-model="form.isPreviousProcess" placeholder="请选择是否自带上工序数量">
            <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否跳工序" prop="allowSkipProcess">
          <el-select v-model="form.allowSkipProcess" placeholder="请选择是否跳工序">
            <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否输入数量" prop="noQuantity">
          <el-select v-model="form.noQuantity" placeholder="请选择是否输入数量">
            <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="操作人工号" prop="operatorCode">
          <el-input v-model="form.operatorCode" placeholder="请输入操作人工号" />
        </el-form-item>
        <el-form-item label="姓名" prop="operatorName">
          <el-input v-model="form.operatorName" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="操作时间" prop="operationTime">
          <el-date-picker clearable v-model="form.operationTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择操作时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ActionInfo" lang="ts">
import { listActionInfo, getActionInfo, delActionInfo, addActionInfo, updateActionInfo, getOrganizationActions } from '@/api/tianxin/actionInfo';
import { ActionInfoVO, ActionInfoQuery, ActionInfoForm } from '@/api/tianxin/actionInfo/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_common_status, is_use, sys_yes_no } = toRefs<any>(proxy?.useDict('sys_common_status', 'is_use', 'sys_yes_no'));

const actionInfoList = ref<ActionInfoVO[]>([]);
// const organizationActions = ref<ActionInfoVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const actionInfoFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: ActionInfoForm = {
  id: undefined,
  action: undefined,
  department: undefined,
  status: undefined,
  sequenceNo: undefined,
  barcodeNo: undefined,
  isQuantityControlled: undefined,
  isPassedQuantity: undefined,
  isScrappedQuantity: undefined,
  isPreviousProcess: undefined,
  allowSkipProcess: undefined,
  noQuantity: undefined,
  operatorCode: undefined,
  operatorName: undefined,
  operationTime: undefined
};
const data = reactive<PageData<ActionInfoForm, ActionInfoQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    action: undefined,
    operatorCode: undefined,
    operatorName: undefined,
    operationTime: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: '主键不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询机床扫描查询列表 */
const getList = async () => {
  loading.value = true;
  const res = await listActionInfo(queryParams.value);
  actionInfoList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 获取组织动作数据 */
/*const getOrganizationActionsData = async () => {
  try {
    const res = await getOrganizationActions();
    organizationActions.value = res.data?.actionList || [];
    console.log('获取组织动作数据:', res.data);
  } catch (error) {
    console.error('获取组织动作数据失败:', error);
    organizationActions.value = [];
  }
};*/

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  actionInfoFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: ActionInfoVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加机床扫描查询';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: ActionInfoVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getActionInfo(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改机床扫描查询';
};

/** 提交按钮 */
const submitForm = () => {
  actionInfoFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateActionInfo(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addActionInfo(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: ActionInfoVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除机床扫描查询编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delActionInfo(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'tianxin/actionInfo/export',
    {
      ...queryParams.value
    },
    `actionInfo_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
  // getOrganizationActionsData();
});
</script>
