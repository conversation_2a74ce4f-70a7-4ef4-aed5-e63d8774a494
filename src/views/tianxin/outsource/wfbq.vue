<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form :model="searchForm" :inline="true">
            <el-form-item label="MO号" prop="moNo">
              <el-input v-model="searchForm.moNo" type="textarea" :rows="3" placeholder="请输入MO号，可输入多行" style="width: 240px" clearable/>
            </el-form-item>
            <el-form-item label="供应商简称" prop="vendorSnm">
              <el-input v-model="searchForm.vendorSnm" placeholder="请输入供应商简称" clearable/>
            </el-form-item>
            <el-form-item label="打印状态" prop="prtSw">
              <el-select v-model="searchForm.prtSw" placeholder="请选择打印状态" clearable>
                <el-option label="未打印" value="N"></el-option>
                <el-option label="已打印" value="Y"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="登图日期" prop="sysDate">
              <el-date-picker
                v-model="sysDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleSearch">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <!-- 移除了多单处理按钮 -->
          <el-col :span="1.5">
            <el-button type="primary" plain>二维码标签</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" plain @click="handleSendEmail">发送邮件</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" :loading="exportLoading" @click="handleExport">
              <el-icon><Download /></el-icon>
              导出Excel
            </el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" />
        </el-row>
      </template>

      <!-- 表格区域 -->
      <el-table v-loading="loading" :data="paginatedData" border style="width: 100%" max-height="100%">
        <el-table-column prop="id" label="ID" min-width="120"></el-table-column>
        <el-table-column prop="moNo" label="MO号" min-width="120"></el-table-column>
        <el-table-column prop="dwgNo" label="图号" min-width="120"></el-table-column>
        <el-table-column prop="customerCode" label="客户代码" min-width="120"></el-table-column>
        <el-table-column prop="poType" label="PO类型" min-width="120"></el-table-column>
        <el-table-column prop="vendorSnm" label="供应商简称" min-width="150"></el-table-column>
        <el-table-column prop="orderQuantity" label="订单数量" min-width="120"></el-table-column>
        <el-table-column prop="processContent" label="工序内容" min-width="150"></el-table-column>
        <el-table-column prop="partClassification" label="零件分类" min-width="120"></el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <el-pagination
        v-show="tableData.length > 0"
        class="mt-4 flex justify-end"
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tableData.length"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 发送邮件对话框 -->
    <el-dialog
      v-model="emailDialogVisible"
      title="发送邮件"
      width="500px"
      :before-close="handleCloseEmailDialog"
    >
      <el-form :model="emailForm" label-width="100px">
        <el-form-item label="附件上传">
          <el-upload
            ref="uploadRef"
            :file-list="fileList"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :before-upload="beforeUpload"
            :auto-upload="false"
            multiple
            drag
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持多文件上传，单个文件大小不超过10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="邮件内容">
          <el-input
            v-model="emailForm.content"
            type="textarea"
            :rows="4"
            placeholder="请输入邮件内容（可选）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseEmailDialog">取消</el-button>
          <el-button type="primary" :loading="sendingEmail" @click="handleConfirmSendEmail">
            发送邮件
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { getOutSourceList } from '@/api/tianxin/processLab/index.js';
import { exportToExcel } from '@/utils/excel';
import { UploadFilled } from '@element-plus/icons-vue';
import type { UploadFile, UploadFiles, UploadInstance } from 'element-plus';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const showSearch = ref(true);
const loading = ref(false);
const exportLoading = ref(false);

// 邮件对话框相关状态
const emailDialogVisible = ref(false);
const sendingEmail = ref(false);
const uploadRef = ref<UploadInstance>();
const fileList = ref<UploadFile[]>([]);

// 邮件表单数据
const emailForm = ref({
  content: ''
});

const sysDate = ref<[DateModelType, DateModelType]>(['', '']);

// 分页相关状态
const currentPage = ref(1);
const pageSize = ref(10);

const searchForm = ref({
  moNo: '', // 改为驼峰形式 moNo
  vendorSnm: '', // 改为驼峰形式 vendorSnm
  prtSw: 'N', // 改为prtSw，未打印默认值为N
  params: {}
});

const tableData = ref([]);
const total = ref(0);

// 计算当前页数据
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return tableData.value.slice(start, end);
});

// 分页大小改变处理
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  currentPage.value = 1; // 重置到第一页
};

// 当前页改变处理
const handleCurrentChange = (val: number) => {
  currentPage.value = val;
};

const handleSearch = async () => {
  try {
    loading.value = true;
    // 添加日期范围参数处理
    searchForm.value.params = {};
    proxy?.addDateRange(searchForm.value, sysDate.value, 'SysDate');
    // 在这里调用实际的API查询方法
    console.log('搜索条件:', searchForm.value);
    const res = await getOutSourceList(searchForm.value);
    tableData.value = res.data || [];
    // 重置分页到第一页
    currentPage.value = 1;
  } catch (error) {
    console.error('查询失败:', error);
    proxy?.modal?.msgError('查询失败');
  } finally {
    loading.value = false;
  }
};

const resetQuery = () => {
  searchForm.value = {
    moNo: '', // 改为驼峰形式 moNo
    vendorSnm: '', // 改为驼峰形式 vendorSnm
    prtSw: 'N', // 改为prtSw，未打印默认值为N
    params: {}
  };
  sysDate.value = ['', '']; // 改为驼峰形式 sysDate
  // 重置分页
  currentPage.value = 1;
  pageSize.value = 10;
};

// 发送邮件功能 - 打开对话框
const handleSendEmail = () => {
  if (tableData.value.length === 0) {
    proxy?.modal?.msgWarning('请先查询数据');
    return;
  }

  // 重置表单和文件列表
  emailForm.value.content = '';
  fileList.value = [];
  emailDialogVisible.value = true;
};

// 文件上传相关处理函数
const handleFileChange = (file: UploadFile, files: UploadFiles) => {
  fileList.value = files;
};

const handleFileRemove = (file: UploadFile, files: UploadFiles) => {
  fileList.value = files;
};

const beforeUpload = (file: File) => {
  // 检查文件大小（10MB限制）
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    proxy?.modal?.msgError('上传文件大小不能超过 10MB!');
    return false;
  }
  return true;
};

// 关闭邮件对话框
const handleCloseEmailDialog = () => {
  emailDialogVisible.value = false;
  emailForm.value.content = '';
  fileList.value = [];
};

// 确认发送邮件
const handleConfirmSendEmail = async () => {
  try {
    sendingEmail.value = true;

    // 这里应该调用实际的发送邮件API
    // 可以将文件列表和邮件内容一起发送
    console.log('发送邮件数据:', {
      tableData: tableData.value,
      files: fileList.value,
      content: emailForm.value.content
    });

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000));

    proxy?.modal?.msgSuccess('邮件发送成功');
    handleCloseEmailDialog();
  } catch (error) {
    console.error('邮件发送失败:', error);
    proxy?.modal?.msgError('邮件发送失败');
  } finally {
    sendingEmail.value = false;
  }
};

// 导出Excel功能
const handleExport = async () => {
  if (tableData.value.length === 0) {
    proxy?.modal?.msgWarning('没有数据可以导出');
    return;
  }

  try {
    exportLoading.value = true;

    // 定义表头映射
    const headers = {
      id: 'ID',
      moNo: 'MO号',
      dwgNo: '图号',
      customerCode: '客户代码',
      poType: 'PO类型',
      vendorSnm: '供应商简称',
      orderQuantity: '订单数量',
      processContent: '工序内容',
      partClassification: '零件分类'
    };

    // 生成文件名
    const filename = `外发标签数据_${new Date().getTime()}`;

    // 使用exportToExcel工具函数导出Excel
    await exportToExcel(tableData.value, headers, filename, '外发标签数据', '67C23A');

    proxy?.modal?.msgSuccess('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    proxy?.modal?.msgError('导出失败');
  } finally {
    exportLoading.value = false;
  }
};
</script>

<style scoped>
.demo-form-inline {
  margin-bottom: 20px;
}
</style>
