<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="MO号" prop="moNo">
              <el-input v-model="queryParams.moNo" placeholder="请输入MO号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="工序号" prop="processNo">
              <el-input v-model="queryParams.processNo" placeholder="请输入工序号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="工序名称" prop="processName">
              <el-input v-model="queryParams.processName" placeholder="请输入工序名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="操作员" prop="operator">
              <el-input v-model="queryParams.operator" placeholder="请输入操作员" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="姓名" prop="operatorName">
              <el-input v-model="queryParams.operatorName" placeholder="请输入姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="机台号" prop="machineNo">
              <el-input v-model="queryParams.machineNo" placeholder="请输入机台号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="动作" prop="action">
              <el-input v-model="queryParams.action" placeholder="请输入动作" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker clearable
                v-model="queryParams.startTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择开始时间"
              />
            </el-form-item>
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker clearable
                v-model="queryParams.endTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择结束时间"
              />
            </el-form-item>
            <el-form-item label="通过数量" prop="passedQuantity">
              <el-input v-model="queryParams.passedQuantity" placeholder="请输入通过数量" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="报废数量" prop="scrappedQuantity">
              <el-input v-model="queryParams.scrappedQuantity" placeholder="请输入报废数量" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="总进度" prop="totalProgress">
              <el-input v-model="queryParams.totalProgress" placeholder="请输入总进度" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="当前进度" prop="currentProgress">
              <el-input v-model="queryParams.currentProgress" placeholder="请输入当前进度" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="工艺工时" prop="processManHours">
              <el-input v-model="queryParams.processManHours" placeholder="请输入工艺工时" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="实际时间" prop="actualTime">
              <el-input v-model="queryParams.actualTime" placeholder="请输入实际时间" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="客户代码" prop="customerCode">
              <el-input v-model="queryParams.customerCode" placeholder="请输入客户代码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="供应商" prop="supplier">
              <el-input v-model="queryParams.supplier" placeholder="请输入供应商" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="储位" prop="storageLocation">
              <el-input v-model="queryParams.storageLocation" placeholder="请输入储位" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="品号" prop="partNo">
              <el-input v-model="queryParams.partNo" placeholder="请输入品号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="图号" prop="drawingNo">
              <el-input v-model="queryParams.drawingNo" placeholder="请输入图号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="物料描述" prop="materialDescription">
              <el-input v-model="queryParams.materialDescription" placeholder="请输入物料描述" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="客户产品料号" prop="customerProductNo">
              <el-input v-model="queryParams.customerProductNo" placeholder="请输入客户产品料号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="客户产品名称" prop="customerProductName">
              <el-input v-model="queryParams.customerProductName" placeholder="请输入客户产品名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="批号" prop="batchNo">
              <el-input v-model="queryParams.batchNo" placeholder="请输入批号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="电脑名" prop="computerName">
              <el-input v-model="queryParams.computerName" placeholder="请输入电脑名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="行业" prop="industry">
              <el-input v-model="queryParams.industry" placeholder="请输入行业" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="排产工时" prop="productionScheduleManHours">
              <el-input v-model="queryParams.productionScheduleManHours" placeholder="请输入排产工时" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="创建时间" prop="creationTime">
              <el-date-picker clearable
                v-model="queryParams.creationTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择创建时间"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:recordLog:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['system:recordLog:edit']">修改</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['system:recordLog:export']">导出</el-button>
          </el-col> -->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="recordLogList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" v-if="true" />
        <el-table-column label="MO号" align="center" prop="moNo" />
        <el-table-column label="图号" align="center" prop="drawingNo" />
        <el-table-column label="工序号" align="center" prop="processNo" />
        <el-table-column label="工序名称" align="center" prop="processName" />
        <el-table-column label="操作员" align="center" prop="operator" />
        <el-table-column label="姓名" align="center" prop="operatorName" />
        <el-table-column label="机台号" align="center" prop="machineNo" />
        <el-table-column label="动作" align="center" prop="action" />
        <el-table-column label="开始时间" align="center" prop="startTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="结束时间" align="center" prop="endTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="通过数量" align="center" prop="passedQuantity" />
        <el-table-column label="报废数量" align="center" prop="scrappedQuantity" />
        <el-table-column label="总进度" align="center" prop="totalProgress" />
        <el-table-column label="当前进度" align="center" prop="currentProgress" />
        <el-table-column label="工艺工时" align="center" prop="processManHours" />
        <el-table-column label="实际工时" align="center" prop="actualTime" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="客户代码" align="center" prop="customerCode" />
        <el-table-column label="供应商" align="center" prop="supplier" />
        <el-table-column label="储位" align="center" prop="storageLocation" />
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="recordLogFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="MO号" prop="moNo">
          <el-input v-model="form.moNo" placeholder="请输入MO号" />
        </el-form-item>
        <el-form-item label="工序号" prop="processNo">
          <el-input v-model="form.processNo" placeholder="请输入工序号" />
        </el-form-item>
        <el-form-item label="工序名称" prop="processName">
          <el-input v-model="form.processName" placeholder="请输入工序名称" />
        </el-form-item>
        <el-form-item label="操作员" prop="operator">
          <el-input v-model="form.operator" placeholder="请输入操作员" />
        </el-form-item>
        <el-form-item label="姓名" prop="operatorName">
          <el-input v-model="form.operatorName" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="机台号" prop="machineNo">
          <el-input v-model="form.machineNo" placeholder="请输入机台号" />
        </el-form-item>
        <el-form-item label="动作" prop="action">
          <el-input v-model="form.action" placeholder="请输入动作" />
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime">
          <el-date-picker clearable
            v-model="form.startTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择开始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime">
          <el-date-picker clearable
            v-model="form.endTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择结束时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="通过数量" prop="passedQuantity">
          <el-input v-model="form.passedQuantity" placeholder="请输入通过数量" />
        </el-form-item>
        <el-form-item label="报废数量" prop="scrappedQuantity">
          <el-input v-model="form.scrappedQuantity" placeholder="请输入报废数量" />
        </el-form-item>
        <el-form-item label="总进度" prop="totalProgress">
          <el-input v-model="form.totalProgress" placeholder="请输入总进度" />
        </el-form-item>
        <el-form-item label="当前进度" prop="currentProgress">
          <el-input v-model="form.currentProgress" placeholder="请输入当前进度" />
        </el-form-item>
        <el-form-item label="工艺工时" prop="processManHours">
          <el-input v-model="form.processManHours" placeholder="请输入工艺工时" />
        </el-form-item>
        <el-form-item label="实际时间" prop="actualTime">
          <el-input v-model="form.actualTime" placeholder="请输入实际时间" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="客户代码" prop="customerCode">
          <el-input v-model="form.customerCode" placeholder="请输入客户代码" />
        </el-form-item>
        <el-form-item label="供应商" prop="supplier">
          <el-input v-model="form.supplier" placeholder="请输入供应商" />
        </el-form-item>
        <el-form-item label="储位" prop="storageLocation">
          <el-input v-model="form.storageLocation" placeholder="请输入储位" />
        </el-form-item>
        <el-form-item label="品号" prop="partNo">
          <el-input v-model="form.partNo" placeholder="请输入品号" />
        </el-form-item>
        <el-form-item label="图号" prop="drawingNo">
          <el-input v-model="form.drawingNo" placeholder="请输入图号" />
        </el-form-item>
        <el-form-item label="物料描述" prop="materialDescription">
          <el-input v-model="form.materialDescription" placeholder="请输入物料描述" />
        </el-form-item>
        <el-form-item label="客户产品料号" prop="customerProductNo">
          <el-input v-model="form.customerProductNo" placeholder="请输入客户产品料号" />
        </el-form-item>
        <el-form-item label="客户产品名称" prop="customerProductName">
          <el-input v-model="form.customerProductName" placeholder="请输入客户产品名称" />
        </el-form-item>
        <el-form-item label="批号" prop="batchNo">
          <el-input v-model="form.batchNo" placeholder="请输入批号" />
        </el-form-item>
        <el-form-item label="电脑名" prop="computerName">
          <el-input v-model="form.computerName" placeholder="请输入电脑名" />
        </el-form-item>
        <el-form-item label="行业" prop="industry">
          <el-input v-model="form.industry" placeholder="请输入行业" />
        </el-form-item>
        <el-form-item label="排产工时" prop="productionScheduleManHours">
          <el-input v-model="form.productionScheduleManHours" placeholder="请输入排产工时" />
        </el-form-item>
        <el-form-item label="创建时间" prop="creationTime">
          <el-date-picker clearable
            v-model="form.creationTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择创建时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="RecordLog" lang="ts">
import { listRecordLog, getRecordLog, delRecordLog, addRecordLog, updateRecordLog } from '@/api/tianxin/scan/index';
import { RecordLogVO, RecordLogQuery, RecordLogForm } from '@/api/tianxin/scan/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const recordLogList = ref<RecordLogVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const recordLogFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: RecordLogForm = {
  id: undefined,
  moNo: undefined,
  processNo: undefined,
  processName: undefined,
  operator: undefined,
  operatorName: undefined,
  machineNo: undefined,
  action: undefined,
  startTime: undefined,
  endTime: undefined,
  passedQuantity: undefined,
  scrappedQuantity: undefined,
  totalProgress: undefined,
  currentProgress: undefined,
  processManHours: undefined,
  actualTime: undefined,
  remark: undefined,
  customerCode: undefined,
  supplier: undefined,
  storageLocation: undefined,
  partNo: undefined,
  drawingNo: undefined,
  materialDescription: undefined,
  customerProductNo: undefined,
  customerProductName: undefined,
  batchNo: undefined,
  computerName: undefined,
  industry: undefined,
  productionScheduleManHours: undefined,
  creationTime: undefined,
}
const data = reactive<PageData<RecordLogForm, RecordLogQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    moNo: undefined,
    processNo: undefined,
    processName: undefined,
    operator: undefined,
    operatorName: undefined,
    machineNo: undefined,
    action: undefined,
    startTime: undefined,
    endTime: undefined,
    passedQuantity: undefined,
    scrappedQuantity: undefined,
    totalProgress: undefined,
    currentProgress: undefined,
    processManHours: undefined,
    actualTime: undefined,
    customerCode: undefined,
    supplier: undefined,
    storageLocation: undefined,
    partNo: undefined,
    drawingNo: undefined,
    materialDescription: undefined,
    customerProductNo: undefined,
    customerProductName: undefined,
    batchNo: undefined,
    computerName: undefined,
    industry: undefined,
    productionScheduleManHours: undefined,
    creationTime: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "主键不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listRecordLog(queryParams.value);
  recordLogList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  recordLogFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: RecordLogVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: RecordLogVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getRecordLog(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息";
}

/** 提交按钮 */
const submitForm = () => {
  recordLogFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateRecordLog(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addRecordLog(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}


onMounted(() => {
  getList();
});
</script>
