<template>
  <div class="p-6">
    <el-card class="max-w-3xl mx-auto" shadow="hover">
      <template #header>
        <div class="flex items-center gap-2">
          <span class="text-lg font-semibold">PDF条形码生成</span>
        </div>
      </template>

      <el-alert
        type="info"
        :closable="false"
        show-icon
        class="mb-4"
        title="支持一次上传最多30个PDF文件，单个文件不超过50MB。系统将自动为每个PDF文件生成条形码并添加到右上角。"
      />

      <el-form ref="uploadFormRef" :model="uploadForm" :rules="uploadRules" label-width="90px" class="mt-2">
        <el-form-item label="选择PDF" prop="files">
          <el-upload
            ref="uploadRef"
            drag
            multiple
            action="#"
            :auto-upload="false"
            :file-list="uploadFileList"
            :limit="30"
            :accept="fileAccept"
            :before-upload="handleBeforeUpload"
            :on-exceed="handleExceed"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            class="w-full"
          >
            <div class="el-upload__text">将文件拖到此处，或点击选择文件</div>
            <template #tip>
              <div class="el-upload__tip">仅支持 .pdf 文件</div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item label="条形码位置" prop="barcodePosition">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="水平位置" prop="barcodeHorizontal">
                <el-select v-model="uploadForm.barcodeHorizontal" placeholder="选择水平位置" style="width: 100%">
                  <el-option label="右边" value="right" />
                  <el-option label="中间" value="center" />
                  <el-option label="左边" value="left" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="边距" prop="barcodeX">
                <el-input-number v-model="uploadForm.barcodeX" :min="0" :max="1000" :step="1" placeholder="边距像素" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="上边距" prop="barcodeY">
                <el-input-number v-model="uploadForm.barcodeY" :min="0" :max="1000" :step="1" placeholder="上边距像素" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
          <div class="text-sm text-gray-500 mt-1">设置条形码的水平位置和距离边缘的距离（像素）</div>
        </el-form-item>

        <el-form-item label="条形码尺寸" prop="barcodeSize">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="宽度" prop="barcodeWidth">
                <el-input-number v-model="uploadForm.barcodeWidth" :min="50" :max="500" :step="10" placeholder="条形码宽度" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="高度" prop="barcodeHeight">
                <el-input-number v-model="uploadForm.barcodeHeight" :min="20" :max="200" :step="5" placeholder="条形码高度" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
          <div class="text-sm text-gray-500 mt-1">设置条形码的尺寸（像素）</div>
        </el-form-item>

        <el-form-item>
          <div class="flex items-center gap-4">
            <el-button :loading="uploadLoading" type="primary" @click="submitUploadGenerate" :disabled="uploadFileList.length === 0">
              生成条形码并下载
            </el-button>
            <el-button @click="resetUpload" :disabled="uploadLoading">清空</el-button>
            <span class="text-sm text-gray-500"> 已选择文件: {{ uploadFileList.length }} 个 </span>
          </div>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup name="PdfBarcode" lang="ts">
import { ComponentInternalInstance, getCurrentInstance, nextTick, reactive, ref, toRefs } from 'vue';
import { ElMessage } from 'element-plus';
import { globalHeaders } from '@/utils/request';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

type UploadForm = {
  files: any[];
  barcodeHorizontal: string;
  barcodeX: number;
  barcodeY: number;
  barcodeWidth: number;
  barcodeHeight: number;
};

const fileAccept = '.pdf';
const uploadLoading = ref(false);
const uploadFormRef = ref<ElFormInstance>();
const uploadRef = ref();
const uploadFileList = ref<any[]>([]);

const initUploadFormData: UploadForm = {
  files: [],
  barcodeHorizontal: 'right', // 默认放在右边
  barcodeX: 20, // 距离边缘20px
  barcodeY: 20, // 距离上边缘20px
  barcodeWidth: 120, // 条形码宽度
  barcodeHeight: 30 // 条形码高度
};

const uploadData = reactive<{ form: UploadForm; rules: ElFormRules }>({
  form: { ...initUploadFormData },
  rules: {
    files: [
      {
        validator: (rule: any, value: any, callback: any) => {
          const list = uploadFileList.value;
          if (!list || (Array.isArray(list) && list.length === 0)) {
            callback(new Error('请至少上传1个PDF文件'));
          } else if (Array.isArray(list) && list.length > 30) {
            callback(new Error('一次最多上传30个PDF文件'));
          } else {
            callback();
          }
        },
        trigger: 'change'
      }
    ],
    barcodeHorizontal: [{ required: true, message: '请选择水平位置', trigger: 'change' }],
    barcodeX: [
      { required: true, message: '边距不能为空', trigger: 'blur' },
      { type: 'number' as const, min: 0, max: 1000, message: '边距必须在0-1000之间', trigger: 'blur' }
    ],
    barcodeY: [
      { required: true, message: 'Y坐标不能为空', trigger: 'blur' },
      { type: 'number' as const, min: 0, max: 1000, message: 'Y坐标必须在0-1000之间', trigger: 'blur' }
    ],
    barcodeWidth: [
      { required: true, message: '宽度不能为空', trigger: 'blur' },
      { type: 'number' as const, min: 50, max: 500, message: '宽度必须在50-500之间', trigger: 'blur' }
    ],
    barcodeHeight: [
      { required: true, message: '高度不能为空', trigger: 'blur' },
      { type: 'number' as const, min: 20, max: 200, message: '高度必须在20-200之间', trigger: 'blur' }
    ]
  }
});

const { form: uploadForm, rules: uploadRules } = toRefs(uploadData);

// 方法
const handleBeforeUpload = (file: File) => {
  // 验证文件类型
  if (!file.name.toLowerCase().endsWith('.pdf')) {
    ElMessage.error('只支持PDF文件');
    return false;
  }

  // 验证文件大小 (50MB)
  const maxSize = 50 * 1024 * 1024;
  if (file.size > maxSize) {
    ElMessage.error(`文件 ${file.name} 过大，最大支持50MB`);
    return false;
  }

  return false; // 阻止自动上传
};

const handleExceed = (files: File[]) => {
  ElMessage.warning(`最多只能选择30个文件，当前已选择${uploadFileList.value.length}个`);
};

const handleFileChange = (file: any, fileList: any[]) => {
  // 更新文件状态
  file.status = 'ready';
  uploadForm.value.files = fileList;
  // 确保 uploadFileList 也同步更新
  uploadFileList.value = fileList;

  console.log('文件变化:', {
    fileName: file.name,
    fileListLength: fileList.length,
    uploadFileListLength: uploadFileList.value.length
  });
};

const handleFileRemove = (file: any, fileList: any[]) => {
  uploadForm.value.files = fileList;
  // 确保 uploadFileList 也同步更新
  uploadFileList.value = fileList;
};

const submitUploadGenerate = async () => {
  try {
    // 验证表单
    await uploadFormRef.value?.validate();

    if (uploadFileList.value.length === 0) {
      ElMessage.warning('请选择要上传的PDF文件');
      return;
    }

    uploadLoading.value = true;

    // 使用fetch API直接调用后端，确保正确处理multipart请求
    try {
      console.log('开始调用API...');

      const formData = new FormData();
      uploadFileList.value.forEach((f: any) => {
        if (f.raw) formData.append('files', f.raw);
      });

      // 添加条形码位置和尺寸参数
      formData.append('barcodeHorizontal', uploadForm.value.barcodeHorizontal);
      formData.append('barcodeX', String(uploadForm.value.barcodeX));
      formData.append('barcodeY', String(uploadForm.value.barcodeY));
      formData.append('barcodeWidth', String(uploadForm.value.barcodeWidth));
      formData.append('barcodeHeight', String(uploadForm.value.barcodeHeight));

      const baseURL = import.meta.env.VITE_APP_BASE_API;
      const headers = globalHeaders();

      console.log('请求URL:', `${baseURL}/mes/pdfBarcode/upload`);
      console.log('请求头:', headers);
      console.log('FormData内容:', formData);

      const resp = await fetch(`${baseURL}/mes/pdfBarcode/upload`, {
        method: 'POST',
        headers: { ...headers }, // 不设置Content-Type，让浏览器自动设置multipart边界
        body: formData
      });

      console.log('响应状态:', resp.status);
      console.log('响应状态文本:', resp.statusText);
      console.log('响应头信息:', resp.headers);
      console.log('响应类型:', resp.type);

      const blob = await resp.blob();
      console.log('Blob数据:', blob);
      console.log('Blob类型:', blob.type);
      console.log('Blob大小:', blob.size);

      // 检查响应是否成功且包含ZIP数据
      const isOk = resp.ok && blob && blob.type !== 'application/json';
      console.log('响应检查结果:', { isOk, respOk: resp.ok, blobExists: !!blob, blobType: blob.type });

      if (isOk) {
        // 成功返回ZIP包
        console.log('检测到ZIP包，开始下载');
        const fileName = `pdf_barcode_${new Date().getTime()}.zip`;
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        a.remove();

        ElMessage.success('PDF条形码生成成功，正在下载...');
        resetUpload();
      } else {
        // 处理失败，尝试解析错误信息
        console.log('未检测到ZIP包，尝试解析错误信息');
        const text = await blob.text();
        console.log('错误响应文本:', text);

        try {
          const obj = JSON.parse(text);
          throw new Error(obj.message || '处理失败，请重试');
        } catch (parseError) {
          console.error('解析错误响应失败:', parseError);
          throw new Error('处理失败，请重试');
        }
      }
    } catch (apiError: any) {
      console.error('API调用失败:', apiError);
      throw new Error(apiError.message || '处理失败，请重试');
    }
  } catch (error: any) {
    ElMessage.error('文件上传失败: ' + (error.response?.data?.msg || error.message || error));
  } finally {
    uploadLoading.value = false;
  }
};

const resetUpload = () => {
  uploadFileList.value = [];
  uploadForm.value = { ...initUploadFormData };
  uploadFormRef.value?.resetFields();
  uploadRef.value?.clearFiles?.();
  nextTick(() => {
    uploadFormRef.value?.clearValidate();
  });
};
</script>

<style scoped>
.el-upload__text {
  color: #606266;
  font-size: 14px;
  text-align: center;
}

.el-upload__tip {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
}

:deep(.el-upload-dragger) {
  width: 100%;
  height: 180px;
}

:deep(.el-upload-list) {
  margin-top: 16px;
}
</style>
