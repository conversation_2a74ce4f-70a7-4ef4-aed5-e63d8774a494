<template>
  <div class="p-6">
    <!-- 功能选择标签页 -->
    <el-tabs v-model="activeTab" class="max-w-4xl mx-auto">
      <!-- PDF转换功能 -->
      <el-tab-pane label="PDF转换" name="pdf">
        <el-card shadow="hover">
          <template #header>
            <div class="flex items-center gap-2">
              <span class="text-lg font-semibold">上传转图</span>
            </div>
          </template>

          <el-alert
            type="info"
            :closable="false"
            show-icon
            class="mb-4"
            title="支持一次上传最多30个PDF文件，单个文件不超过150MB。可设置PDF转换缩放比例（0.10 - 1.00），默认0.94。"
          />

          <el-form ref="uploadFormRef" :model="uploadForm" :rules="uploadRules" label-width="90px" class="mt-2">
            <el-form-item label="选择PDF" prop="files">
              <el-upload
                ref="uploadRef"
                drag
                multiple
                action="#"
                :auto-upload="false"
                :file-list="uploadFileList"
                :limit="30"
                :accept="fileAccept"
                :before-upload="handleBeforeUpload"
                :on-exceed="handleExceed"
                :on-change="handleFileChange"
                :on-remove="handleFileRemove"
                class="w-full"
              >
                <div class="el-upload__text">将文件拖到此处，或点击选择文件</div>
                <template #tip>
                  <div class="el-upload__tip">仅支持 .pdf 文件</div>
                </template>
              </el-upload>
            </el-form-item>

            <el-form-item label="缩放比例" prop="scale">
              <el-input-number
                v-model="uploadForm.scale"
                :min="0.1"
                :max="1"
                :step="0.01"
                :precision="2"
                placeholder="请输入缩放比例"
                style="width: 200px"
              />
              <div class="text-sm text-gray-500 mt-1">设置PDF转换的缩放比例，范围 0.10 - 1.00，默认 0.94（94%）</div>
            </el-form-item>

            <el-form-item>
              <el-button :loading="uploadLoading" type="primary" @click="submitUploadGenerate" :disabled="uploadFileList.length === 0"
                >生成并下载</el-button
              >
              <el-button @click="resetUpload" :disabled="uploadLoading">清空</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>

      <!-- PLM图纸下载功能 -->
      <el-tab-pane label="PLM图纸下载" name="plm">
        <el-card shadow="hover">
          <template #header>
            <div class="flex items-center gap-2">
              <span class="text-lg font-semibold">PLM图纸批量下载</span>
            </div>
          </template>

          <el-alert
            type="info"
            :closable="false"
            show-icon
            class="mb-4"
            title="支持上传Excel文件，系统将自动解析图号&版本列（第E列）的数据，并从PLM系统中批量下载对应的图纸文件，最终打包成ZIP文件供下载。"
          />

          <el-alert
            type="warning"
            :closable="false"
            show-icon
            class="mb-4"
            title="性能提示：建议单次处理图号数量不超过500个，大量图纸下载可能需要较长时间，请耐心等待。系统会根据图号数量自动调整超时时间。"
          />

          <el-form ref="plmFormRef" :model="plmForm" :rules="plmRules" label-width="120px" class="mt-2">
            <el-form-item label="Excel模板">
              <div class="template-download-section">
                <div class="template-download-card">
                  <div class="template-icon">
                    <el-icon size="24"><Document /></el-icon>
                  </div>
                  <div class="template-content">
                    <div class="template-title">Excel模板下载</div>
                    <div class="template-desc">请先下载模板文件，按照模板格式填写图号数据后上传</div>
                    <el-button type="primary" @click="downloadTemplate" :loading="templateLoading" class="template-download-btn" size="default">
                      <el-icon class="mr-1"><Download /></el-icon>
                      下载Excel模板
                    </el-button>
                  </div>
                </div>
              </div>
            </el-form-item>

            <el-form-item label="选择Excel文件" prop="file">
              <el-upload
                ref="plmUploadRef"
                drag
                action="#"
                :auto-upload="false"
                :file-list="plmFileList"
                :limit="1"
                :accept="plmFileAccept"
                :before-upload="handlePlmBeforeUpload"
                :on-exceed="handlePlmExceed"
                :on-change="handlePlmFileChange"
                :on-remove="handlePlmFileRemove"
                class="w-full"
              >
                <div class="el-upload__text">将Excel文件拖到此处，或点击选择文件</div>
                <template #tip>
                  <div class="el-upload__tip">
                    <div>仅支持 .xlsx 和 .xls 文件</div>
                    <div class="text-sm text-gray-500 mt-1">请上传Excel文件，系统将自动解析图号&版本列（第E列）并下载对应的PLM图纸</div>
                  </div>
                </template>
              </el-upload>
            </el-form-item>

            <el-form-item>
              <el-button :loading="plmLoading" type="primary" @click="submitPlmDownload" :disabled="plmFileList.length === 0"
                >开始下载PLM图纸</el-button
              >
              <el-button @click="resetPlmUpload" :disabled="plmLoading">清空</el-button>
            </el-form-item>
          </el-form>

          <!-- PLM下载进度显示 -->
          <div v-if="plmProgress.show" class="mt-6">
            <el-card>
              <template #header>
                <div class="flex items-center gap-2">
                  <span class="text-md font-semibold">下载进度</span>
                </div>
              </template>

              <div class="space-y-4">
                <div>
                  <el-progress
                    :percentage="plmProgress.percentage"
                    :status="plmProgress.status"
                    :stroke-width="10"
                    :show-text="true"
                    :format="(percentage) => `${percentage}%`"
                  />
                  <div class="text-sm text-gray-600 mt-2 text-center">
                    {{ plmProgress.percentage < 100 ? '正在处理中，请稍候...' : '处理完成' }}
                  </div>
                </div>

                <div v-if="plmProgress.logs.length > 0" class="max-h-40 overflow-y-auto">
                  <div class="text-sm font-medium mb-2">处理日志:</div>
                  <div class="space-y-1">
                    <div v-for="(log, index) in plmProgress.logs" :key="index" :class="getLogClass(log.type)" class="text-xs p-2 rounded">
                      {{ log.message }}
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup name="ConvertRecord" lang="ts">
import { ComponentInternalInstance, getCurrentInstance, nextTick, reactive, ref, toRefs } from 'vue';
import { globalHeaders } from '@/utils/request';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

type UploadForm = { files: any[]; scale: number };
type PlmForm = { file: any };

// 标签页控制
const activeTab = ref('pdf');

// PDF转换相关
const fileAccept = '.pdf';
const uploadLoading = ref(false);
const uploadFormRef = ref<ElFormInstance>();
const uploadRef = ref();
const uploadFileList = ref<any[]>([]);

// PLM下载相关
const plmFileAccept = '.xlsx,.xls';
const plmLoading = ref(false);
const templateLoading = ref(false);
const plmFormRef = ref<ElFormInstance>();
const plmUploadRef = ref();
const plmFileList = ref<any[]>([]);

// PDF转换表单数据
const initUploadFormData: UploadForm = { files: [], scale: 0.94 };

const uploadData = reactive<{ form: UploadForm; rules: ElFormRules }>({
  form: { ...initUploadFormData },
  rules: {
    files: [
      {
        validator: (rule: any, value: any, callback: any) => {
          const list = uploadFileList.value;
          if (!list || (Array.isArray(list) && list.length === 0)) {
            callback(new Error('请至少上传1个PDF文件'));
          } else if (Array.isArray(list) && list.length > 30) {
            callback(new Error('一次最多上传30个PDF文件'));
          } else {
            callback();
          }
        },
        trigger: 'change'
      }
    ],
    scale: [
      { required: true, message: '缩放比例不能为空', trigger: 'blur' },
      { type: 'number' as const, min: 0.1, max: 1, message: '缩放比例必须在0.1-1.0之间', trigger: 'blur' }
    ]
  }
});

const { form: uploadForm, rules: uploadRules } = toRefs(uploadData);

// PLM下载表单数据
const initPlmFormData: PlmForm = { file: null };

const plmData = reactive<{ form: PlmForm; rules: ElFormRules }>({
  form: { ...initPlmFormData },
  rules: {
    file: [
      {
        validator: (rule: any, value: any, callback: any) => {
          const list = plmFileList.value;
          if (!list || (Array.isArray(list) && list.length === 0)) {
            callback(new Error('请上传Excel文件'));
          } else {
            callback();
          }
        },
        trigger: 'change'
      }
    ]
  }
});

const { form: plmForm, rules: plmRules } = toRefs(plmData);

// PLM下载进度相关
const plmProgress = reactive({
  show: false,
  percentage: 0,
  status: 'success' as 'success' | 'exception' | 'warning',
  logs: [] as Array<{ type: 'info' | 'success' | 'error' | 'warning'; message: string }>
});

const handleBeforeUpload = (file: any) => {
  const isPdf = file.type === 'application/pdf' || /\.pdf$/i.test(file.name);
  if (!isPdf) {
    proxy?.$modal.msgError('仅支持上传PDF文件');
    return false;
  }
  const isLt150M = file.size / 1024 / 1024 < 150;
  if (!isLt150M) {
    proxy?.$modal.msgError('单个文件不能超过150MB');
    return false;
  }
  return true;
};

const handleExceed = () => {
  proxy?.$modal.msgError('上传文件数量不能超过30个');
};

const handleFileChange = (file: any, fileList: any[]) => {
  uploadFileList.value = fileList;
  nextTick(() => uploadFormRef.value?.validateField('files'));
};

const handleFileRemove = (file: any, fileList: any[]) => {
  uploadFileList.value = fileList;
  nextTick(() => uploadFormRef.value?.validateField('files'));
};

const resetUpload = () => {
  uploadFileList.value = [];
  uploadForm.value = { ...initUploadFormData };
  uploadRef.value?.clearFiles?.();
  nextTick(() => uploadFormRef.value?.clearValidate());
};

// PLM下载相关方法
const handlePlmBeforeUpload = (file: any) => {
  const isExcel =
    file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    file.type === 'application/vnd.ms-excel' ||
    /\.(xlsx|xls)$/i.test(file.name);
  if (!isExcel) {
    proxy?.$modal.msgError('仅支持上传Excel文件(.xlsx/.xls)');
    return false;
  }
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    proxy?.$modal.msgError('Excel文件不能超过10MB');
    return false;
  }
  return true;
};

const handlePlmExceed = () => {
  proxy?.$modal.msgError('一次只能上传一个Excel文件');
};

const handlePlmFileChange = (file: any, fileList: any[]) => {
  plmFileList.value = fileList;
  nextTick(() => plmFormRef.value?.validateField('file'));
};

const handlePlmFileRemove = (file: any, fileList: any[]) => {
  plmFileList.value = fileList;
  nextTick(() => plmFormRef.value?.clearValidate());
};

const resetPlmUpload = () => {
  plmFileList.value = [];
  plmForm.value = { ...initPlmFormData };
  plmUploadRef.value?.clearFiles?.();
  plmProgress.show = false;
  plmProgress.logs = [];
  nextTick(() => plmFormRef.value?.clearValidate());
};

// 下载Excel模板
const downloadTemplate = async () => {
  try {
    templateLoading.value = true;

    const baseURL = import.meta.env.VITE_APP_BASE_API;
    const headers = globalHeaders();

    const response = await fetch(`${baseURL}/mes/convertRecord/downloadTemplate`, {
      method: 'GET',
      headers: { ...headers }
    });

    if (response.ok) {
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'drwa_download_template.xlsx';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      a.remove();

      proxy?.$modal.msgSuccess('Excel模板下载成功！');
    } else {
      const text = await response.text();
      let errorMessage = '模板下载失败，请重试';
      try {
        const obj = JSON.parse(text);
        errorMessage = obj.error || errorMessage;
      } catch {
        errorMessage = `服务器错误 (状态码: ${response.status})`;
      }
      proxy?.$modal.msgError(errorMessage);
    }
  } catch (error) {
    console.error('下载模板时发生错误:', error);
    proxy?.$modal.msgError('模板下载失败，请重试');
  } finally {
    templateLoading.value = false;
  }
};

const getLogClass = (type: string) => {
  switch (type) {
    case 'success':
      return 'bg-green-50 text-green-700 border border-green-200';
    case 'error':
      return 'bg-red-50 text-red-700 border border-red-200';
    case 'warning':
      return 'bg-yellow-50 text-yellow-700 border border-yellow-200';
    default:
      return 'bg-blue-50 text-blue-700 border border-blue-200';
  }
};

const submitUploadGenerate = () => {
  uploadFormRef.value?.validate(async (valid: boolean) => {
    if (!valid) return;
    if (!uploadFileList.value || uploadFileList.value.length === 0) return;
    try {
      uploadLoading.value = true;
      const formData = new FormData();
      uploadFileList.value.forEach((f: any) => {
        if (f.raw) formData.append('files', f.raw);
      });
      if (uploadForm.value.scale) {
        formData.append('scale', String(uploadForm.value.scale));
      }
      const baseURL = import.meta.env.VITE_APP_BASE_API;
      const headers = globalHeaders();
      const resp = await fetch(`${baseURL}/mes/convertRecord/batchUploadAndConvertWithZip`, {
        method: 'POST',
        headers: { ...headers },
        body: formData
      });
      const blob = await resp.blob();
      const isOk = resp.ok && blob && blob.type !== 'application/json';
      if (isOk) {
        const fileName = `convert_upload_${new Date().getTime()}.zip`;
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        a.remove();
        proxy?.$modal.msgSuccess('ZIP包生成成功，正在下载...');
        resetUpload();
      } else {
        const text = await blob.text();
        try {
          const obj = JSON.parse(text);
          proxy?.$modal.msgError(obj.msg || 'ZIP包生成失败，请重试');
        } catch {
          proxy?.$modal.msgError('ZIP包生成失败，请重试');
        }
      }
    } catch (error) {
      proxy?.$modal.msgError('ZIP包生成失败，请重试');
    } finally {
      uploadLoading.value = false;
    }
  });
};

// PLM下载提交方法
const submitPlmDownload = () => {
  plmFormRef.value?.validate(async (valid: boolean) => {
    if (!valid) return;
    if (!plmFileList.value || plmFileList.value.length === 0) return;

    try {
      plmLoading.value = true;
      plmProgress.show = true;
      plmProgress.percentage = 0;
      plmProgress.status = 'success';
      plmProgress.logs = [];

      // 添加开始日志
      plmProgress.logs.push({
        type: 'info',
        message: `开始处理Excel文件: ${plmFileList.value[0].name}`
      });

      const formData = new FormData();
      if (plmFileList.value[0].raw) {
        formData.append('file', plmFileList.value[0].raw);
      }

      const baseURL = import.meta.env.VITE_APP_BASE_API;
      const headers = globalHeaders();

      plmProgress.logs.push({
        type: 'info',
        message: '正在上传Excel文件并解析图号数据...'
      });

      // 简化的进度显示
      plmProgress.percentage = 50;
      plmProgress.logs.push({
        type: 'info',
        message: '正在处理中，请稍候...'
      });

      const resp = await fetch(`${baseURL}/mes/convertRecord/batchDownloadFromExcel`, {
        method: 'POST',
        headers: { ...headers },
        body: formData
      });

      // 处理完成

      const blob = await resp.blob();
      const isOk = resp.ok && blob && blob.type !== 'application/json';

      if (isOk) {
        // 进度条显示完成状态
        plmProgress.percentage = 100;
        plmProgress.status = 'success';

        plmProgress.logs.push({
          type: 'success',
          message: 'PLM图纸下载完成，正在生成ZIP文件...'
        });

        const fileName = `plm_drawings_${new Date().getTime()}.zip`;
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        a.remove();

        plmProgress.logs.push({
          type: 'success',
          message: `ZIP文件下载成功: ${fileName}`
        });

        plmProgress.logs.push({
          type: 'success',
          message: '处理完成！'
        });

        proxy?.$modal.msgSuccess('PLM图纸批量下载完成！');

        // 3秒后重置表单
        setTimeout(() => {
          resetPlmUpload();
        }, 3000);
      } else {
        let errorMessage = 'PLM图纸下载失败，请重试';
        try {
          const text = await blob.text();
          console.log('服务器响应文本:', text);

          if (text && text.trim()) {
            try {
              const obj = JSON.parse(text);
              errorMessage = obj.error || obj.msg || errorMessage;
            } catch (parseError) {
              console.error('JSON解析失败:', parseError);
              // 如果不是JSON格式，直接使用响应文本
              errorMessage = text.length > 100 ? text.substring(0, 100) + '...' : text;
            }
          }
        } catch (textError) {
          console.error('读取响应文本失败:', textError);
          errorMessage = `服务器错误 (状态码: ${resp.status})`;
        }

        plmProgress.logs.push({
          type: 'error',
          message: `下载失败: ${errorMessage}`
        });
        plmProgress.status = 'exception';
        proxy?.$modal.msgError(errorMessage);
      }
    } catch (error) {
      plmProgress.logs.push({
        type: 'error',
        message: `下载过程发生错误: ${error}`
      });
      plmProgress.status = 'exception';
      proxy?.$modal.msgError('PLM图纸下载失败，请重试');
    } finally {
      plmLoading.value = false;
    }
  });
};
</script>

<style scoped>
.el-upload__tip {
  margin-top: 7px;
  color: #606266;
  font-size: 12px;
  line-height: 1.4;
}

.max-h-40 {
  max-height: 10rem;
}

.space-y-1 > * + * {
  margin-top: 0.25rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

/* 模板下载区域样式 */
.template-download-section {
  width: 100%;
}

.template-download-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.template-download-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.template-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.template-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.template-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.template-desc {
  font-size: 14px;
  color: #64748b;
  line-height: 1.5;
  margin: 0;
}

/* 模板下载按钮样式 */
.template-download-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  align-self: flex-start;
}

.template-download-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.template-download-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.template-download-btn .el-icon {
  font-size: 16px;
  margin-right: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .template-download-card {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .template-content {
    align-items: center;
  }

  .template-download-btn {
    align-self: center;
  }
}
</style>
