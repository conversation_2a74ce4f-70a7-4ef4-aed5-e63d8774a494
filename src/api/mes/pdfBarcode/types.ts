export interface PdfBarcodeVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * 任务ID
   */
  taskId: string;

  /**
   * 原始文件名
   */
  originalFileName: string;

  /**
   * 处理后的文件名
   */
  processedFileName: string;

  /**
   * 文件大小（字节）
   */
  fileSize: number;

  /**
   * 处理状态：0-处理中，1-成功，2-失败
   */
  status: number;

  /**
   * 错误信息
   */
  errorMessage: string;

  /**
   * 创建时间
   */
  createTime: string;

  /**
   * 完成时间
   */
  finishTime: string;
}

export interface PdfBarcodeForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 任务ID
   */
  taskId?: string;

  /**
   * 原始文件名
   */
  originalFileName?: string;

  /**
   * 处理后的文件名
   */
  processedFileName?: string;

  /**
   * 文件大小（字节）
   */
  fileSize?: number;

  /**
   * 处理状态：0-处理中，1-成功，2-失败
   */
  status?: number;

  /**
   * 错误信息
   */
  errorMessage?: string;

  /**
   * 创建时间
   */
  createTime?: string;

  /**
   * 完成时间
   */
  finishTime?: string;
}

export interface PdfBarcodeQuery extends PageQuery {
  /**
   * 任务ID
   */
  taskId?: string;

  /**
   * 原始文件名
   */
  originalFileName?: string;

  /**
   * 处理状态
   */
  status?: number;

  /**
   * 日期范围参数
   */
  params?: any;
}

export interface PdfBarcodeUploadResponse {
  /**
   * 任务ID
   */
  taskId: string;

  /**
   * 处理状态
   */
  status: number;

  /**
   * 消息
   */
  msg: string;

  /**
   * 数据
   */
  data: {
    /**
     * ZIP文件数据（Base64编码）
     */
    zipData: string;
    
    /**
     * 文件名
     */
    filename: string;
    
    /**
     * 处理结果统计
     */
    summary: {
      total: number;
      success: number;
      failed: number;
      files: PdfBarcodeVO[];
    };
  };
}

export interface PdfBarcodeStatusResponse {
  /**
   * 任务ID
   */
  taskId: string;

  /**
   * 处理状态
   */
  status: number;

  /**
   * 进度百分比
   */
  progress: number;

  /**
   * 处理结果
   */
  result?: {
    total: number;
    success: number;
    failed: number;
    files: PdfBarcodeVO[];
  };

  /**
   * 错误信息
   */
  errorMessage?: string;
}
