import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { PdfBarcodeVO, PdfBarcodeForm, PdfBarcodeQuery, PdfBarcodeUploadResponse, PdfBarcodeStatusResponse } from '@/api/mes/pdfBarcode/types';

/**
 * 批量上传PDF文件并处理
 * @param files PDF文件数组
 * @returns ZIP包数据
 */
export const uploadAndProcessPdfFiles = (files: File[]): AxiosPromise<Blob> => {
  const formData = new FormData();
  files.forEach((file) => {
    formData.append('files', file);
  });

  return request({
    url: '/mes/pdfBarcode/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    responseType: 'blob', // 返回二进制数据
    timeout: 300000 // 5分钟超时，因为处理PDF可能需要较长时间
  });
};

/**
 * 下载处理结果ZIP包
 * @param taskId 任务ID
 * @returns ZIP文件流
 */
export const downloadResult = (taskId: string): AxiosPromise<Blob> => {
  return request({
    url: `/mes/pdfBarcode/download`,
    method: 'post',
    data: { taskId },
    responseType: 'blob', // 返回二进制数据
    timeout: 60000 // 1分钟超时
  });
};

/**
 * 查询处理状态
 * @param taskId 任务ID
 * @returns 处理状态信息
 */
export const getProcessStatus = (taskId: string): AxiosPromise<PdfBarcodeStatusResponse> => {
  return request({
    url: `/mes/pdfBarcode/status/${taskId}`,
    method: 'get',
    timeout: 10000 // 10秒超时
  });
};

/**
 * 清理临时文件
 * @param taskId 任务ID
 * @returns 清理结果
 */
export const cleanupTempFiles = (taskId: string): AxiosPromise<any> => {
  return request({
    url: `/mes/pdfBarcode/cleanup/${taskId}`,
    method: 'delete',
    timeout: 10000 // 10秒超时
  });
};

/**
 * 健康检查
 * @returns 服务状态
 */
export const checkHealth = (): AxiosPromise<any> => {
  return request({
    url: '/mes/pdfBarcode/health',
    method: 'get',
    timeout: 5000 // 5秒超时
  });
};

/**
 * 测试上传接口
 * @param files PDF文件数组
 * @returns 测试结果
 */
export const testUpload = (files: File[]): AxiosPromise<any> => {
  const formData = new FormData();
  files.forEach((file) => {
    formData.append('files', file);
  });

  return request({
    url: '/mes/pdfBarcode/test',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 30000 // 30秒超时
  });
};

/**
 * 测试ZIP响应接口
 * @returns ZIP包数据
 */
export const testZipResponse = (): AxiosPromise<Blob> => {
  return request({
    url: '/mes/pdfBarcode/testZip',
    method: 'post',
    responseType: 'blob',
    timeout: 30000 // 30秒超时
  });
};

/**
 * 查询PDF条形码处理记录列表
 * @param query 查询条件
 * @returns 记录列表
 */
export const listPdfBarcode = (query?: PdfBarcodeQuery): AxiosPromise<PdfBarcodeVO[]> => {
  return request({
    url: '/mes/pdfBarcode/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询PDF条形码处理记录详情
 * @param id 记录ID
 * @returns 记录详情
 */
export const getPdfBarcode = (id: string | number): AxiosPromise<PdfBarcodeVO> => {
  return request({
    url: '/mes/pdfBarcode/' + id,
    method: 'get'
  });
};

/**
 * 新增PDF条形码处理记录
 * @param data 记录数据
 * @returns 操作结果
 */
export const addPdfBarcode = (data: PdfBarcodeForm): AxiosPromise<any> => {
  return request({
    url: '/mes/pdfBarcode',
    method: 'post',
    data: data
  });
};

/**
 * 修改PDF条形码处理记录
 * @param data 记录数据
 * @returns 操作结果
 */
export const updatePdfBarcode = (data: PdfBarcodeForm): AxiosPromise<any> => {
  return request({
    url: '/mes/pdfBarcode',
    method: 'put',
    data: data
  });
};

/**
 * 删除PDF条形码处理记录
 * @param id 记录ID
 * @returns 操作结果
 */
export const delPdfBarcode = (id: string | number | Array<string | number>): AxiosPromise<any> => {
  return request({
    url: '/mes/pdfBarcode/' + id,
    method: 'delete'
  });
};
