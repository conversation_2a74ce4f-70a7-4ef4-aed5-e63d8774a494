/**
 * 单工序统计查询参数
 */
export interface SingleProcessQuery {
  /** 厂商简称 */
  vendorName?: string;
  /** MO单号 */
  moNo?: string;
  /** 客户代码 */
  customerCode?: string;
  /** 客户类别 */
  customerType?: string;
  /** PMC负责人 */
  pmcManager?: string;
  /** 已发供应商 */
  supplierStatus?: string;
  /** 关闭否 */
  closed?: string;
  /** 工序类别 */
  processType?: string;
}

/**
 * 统计信息数据
 */
export interface StatisticData {
  /** ID */
  id?: number;
  /** 利润中心 */
  profitCenter?: string;
  /** PMC跟单员 */
  pmcManager?: string;
  /** 图纸统计 */
  drawingCount?: number;
  /** 交期达成 */
  onTimeCount?: number;
  /** 交期延误 */
  delayCount?: number;
  /** 品质退货数 */
  qualityReturnCount?: number;
  /** 达成率 */
  achievementRate?: string;
  /** 实际达成率 */
  actualAchievementRate?: string;
}

/**
 * 外发明细数据
 */
export interface OutsourcingDetailData {
  /** MO状态 */
  moStatus?: string;
  /** 供应商简称 */
  vendorShortName?: string;
  /** MO号 */
  moNo?: string;
  /** 图号 */
  drawingNo?: string;
  /** 客户代码 */
  customerCode?: string;
  /** PR数量 */
  prQuantity?: number;
  /** PO数量 */
  poQuantity?: number;
  /** PO欠数 */
  poShortage?: number;
  /** 外发时间 */
  outsourceDate?: string;
  /** PMC要求交期 */
  pmcRequestDate?: string;
  /** 跟单负责人 */
  pmcManager?: string;
  /** 外发跟单员 */
  outsourceManager?: string;
  /** 末工序时间 */
  lastProcessTime?: string;
  /** 客户要求交期 */
  customerDueDate?: string;
  /** 是否供料 */
  materialProvided?: string;
  /** 是否已出PO */
  poIssued?: string;
  /** PO号 */
  poNo?: string;
  /** PO项次 */
  poItem?: string;
  /** PO审核 */
  poReview?: string;
  /** PO创建日期 */
  poCreateDate?: string;
  /** 利润中心 */
  profitCenter?: string;
}