import request from '@/utils/request';
import { AxiosPromise } from 'axios';

import { SingleProcessQuery, StatisticData, OutsourcingDetailData } from './types';

export const PMC_REPORTS_BASE_URL = '/pmc/process/report';
/**
 * 查询单工序统计数据
 * @param query 查询参数
 */
export function getSingleProcessStatistics(query: SingleProcessQuery): AxiosPromise<{
  rows: StatisticData[];
  total: number;
  details: OutsourcingDetailData[];
  detailTotal: number;
}> {
  return request({
    url: `${PMC_REPORTS_BASE_URL}/processStatistics/list`,
    method: 'get',
    params: query
  });
}


export function getEntireProcessStatistics(query: SingleProcessQuery): AxiosPromise<{
  rows: StatisticData[];
  total: number;
  details: OutsourcingDetailData[];
  detailTotal: number;
}> {
  return request({
    url: `${PMC_REPORTS_BASE_URL}/processStatistics/entire/list`,
    method: 'get',
    params: query
  });
}


/**
 * 下载绘图
 * @param query 查询参数
 */
export function downloadDraw(query: { moNo: string; drawingNo: string }): AxiosPromise<string> {
  return request({
    url: `${PMC_REPORTS_BASE_URL}/processStatistics/download/draw`,
    method: 'get',
    params: query
  });
}



