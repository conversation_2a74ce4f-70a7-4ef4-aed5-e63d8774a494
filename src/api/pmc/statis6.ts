import request from '@/utils/request';

export const PMC_MO_MANAGER_BASE_URL = '/pmc/mo';

/**
 * 加载装配订单管理数据
 */
export function loadStatis6Data() {
  return request({
    url: `${PMC_MO_MANAGER_BASE_URL}/statis6/loadData`,
    method: 'get',
    timeout: 3 * 60 * 1000
  });
}

/**
 * 查询装配订单管理数据
 * @param data 查询参数
 */
export function getStatis6Data(data: object = {}) {
  return request({
    url: `${PMC_MO_MANAGER_BASE_URL}/statis6/search`,
    method: 'get',
    params: data
  });
}

/**
 * 保存装配订单管理数据
 * @param data 保存的数据
 */
export function editStatis6Data(data: object = {}) {
  return request({
    url: `${PMC_MO_MANAGER_BASE_URL}/statis6/edit`,
    method: 'post',
    data: data
  });
}

/**
 * 获取物料信息
 * @param data 查询参数
 */
export function getMaterialData(data: object = {}) {
  return request({
    url: `${PMC_MO_MANAGER_BASE_URL}/statis6/material`,
    method: 'get',
    params: data
  });
}
