import request from '@/utils/request';
import axios, { AxiosPromise } from 'axios';
import qs from 'qs';

export const getStatistics = (query?: any): AxiosPromise<any> => {
  return request({
    url: '/pmc/very-urgent-follow/statistics',
    method: 'get',
    params: query
  });
};

export const getMerchandisers = (): AxiosPromise<any> => {
  return request({
    url: '/pmc/very-urgent-follow/merchandiser',
    method: 'get'
  });
};

export const getMoDetails = (moList: string[]): AxiosPromise<any> => {
  return request({
    url: '/pmc/very-urgent-follow/details',
    method: 'post',
    data: qs.stringify({
      moNos: moList.join(',')
    }),
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  });
};
