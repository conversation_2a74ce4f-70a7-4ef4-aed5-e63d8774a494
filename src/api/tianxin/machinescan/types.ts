export interface MachineScanVO {
  /**
   * MO号
   */
  moNo: string;

  /**
   * 工序号
   */
  processNo: number;

  /**
   * 工序名称
   */
  processName: string;

  /**
   * 操作员
   */
  operator: string;

  /**
   * 姓名
   */
  operatorName: string;

  /**
   * 机台号
   */
  machineNo: string;

  /**
   * 动作
   */
  action: string;

  /**
   * 开始时间
   */
  startTime: string;

  /**
   * 结束时间
   */
  endTime: string;

  /**
   * 通过数量
   */
  passedQuantity: number;

  /**
   * 报废数量
   */
  scrappedQuantity: number;

  /**
   * 总进度
   */
  totalProgress: number;

  /**
   * 当前进度
   */
  currentProgress: number;

  /**
   * 工艺工时
   */
  processManHours: number;

  /**
   * 实际时间
   */
  actualTime: number;

  /**
   * 客户代码
   */
  customerCode: string;

  /**
   * 计划开始时间
   */
  plannedStartTime: string;

  /**
   * 计划结束时间
   */
  plannedEndTime: string;
}

export interface MachineScanQuery extends PageQuery {
  /**
   * 动作
   */
  action?: string;

  /**
   * 机床
   */
  machines?: string[];
}
