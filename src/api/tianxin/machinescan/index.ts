import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { MachineScanVO, MachineScanForm, MachineScanQuery } from '@/api/tianxin/machinescan/types';

/**
 * 查询机床扫描查询列表
 * @param query
 * @returns {*}
 */

export const listMachineScan = (query?: MachineScanQuery): AxiosPromise<MachineScanVO[]> => {
  return request({
    url: '/tianxin/machinescan/list',
    method: 'get',
    params: query
  });
};

export const listAllMachines = (): AxiosPromise<string[]> => {
  return request({
    url: '/tianxin/machinescan/list_machine',
    method: 'get'
  });
};

/**
 * 查询机床扫描查询详细
 * @param id
 */
export const getMachineScan = (id: string | number): AxiosPromise<MachineScanVO> => {
  return request({
    url: '/tianxin/machinescan/' + id,
    method: 'get'
  });
};
