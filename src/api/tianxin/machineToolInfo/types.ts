export interface MachineToolInfoVO {
  id: string | number;
  /**
   * 机床
   */
  machineTool: string;

  /**
   * 资源
   */
  resource: string;

  /**
   * 序号
   */
  serialNo: number;

  /**
   * 可扫描
   */
  isScannable: string;

  /**
   * 分组
   */
  grouping: string;

  /**
   * 类别
   */
  category: string;

  /**
   * 使用状态
   */
  usageStatus: string;

  /**
   * 停用日期
   */
  deactivationDate: string;

  /**
   * 备注
   */
  remarks: string;

  /**
   * 操作人
   */
  operator: string;

  /**
   * 操作时间
   */
  operationTime: string;
}

export interface MachineToolInfoForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 机床
   */
  machineTool?: string;

  /**
   * 资源
   */
  resource?: string;

  /**
   * 序号
   */
  serialNo?: number;

  /**
   * 可扫描
   */
  isScannable?: string;

  /**
   * 分组
   */
  grouping?: string;

  /**
   * 类别
   */
  category?: string;

  /**
   * 使用状态
   */
  usageStatus?: string;

  /**
   * 停用日期
   */
  deactivationDate?: string;

  /**
   * 备注
   */
  remarks?: string;

  /**
   * 操作人
   */
  operator?: string;

  /**
   * 操作时间
   */
  operationTime?: string;
}

export interface MachineToolInfoQuery extends PageQuery {
  /**
   * 机床
   */
  machineTool?: string;

  /**
   * 资源
   */
  resource?: string;

  /**
   * 可扫描
   */
  isScannable?: string;

  /**
   * 类别
   */
  category?: string;

  /**
   * 操作人
   */
  operator?: string;

  /**
   * 操作时间
   */
  operationTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
