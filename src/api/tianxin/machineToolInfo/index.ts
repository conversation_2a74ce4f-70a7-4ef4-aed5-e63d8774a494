import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { MachineToolInfoVO, MachineToolInfoForm, MachineToolInfoQuery } from '@/api/tianxin/machineToolInfo/types';

/**
 * 查询机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等列表
 * @param query
 * @returns {*}
 */

export const listMachineToolInfo = (query?: MachineToolInfoQuery): AxiosPromise<MachineToolInfoVO[]> => {
  return request({
    url: '/tianxin/machineToolInfo/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等详细
 * @param id
 */
export const getMachineToolInfo = (id: string | number): AxiosPromise<MachineToolInfoVO> => {
  return request({
    url: '/tianxin/machineToolInfo/' + id,
    method: 'get'
  });
};

/**
 * 新增机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等
 * @param data
 */
export const addMachineToolInfo = (data: MachineToolInfoForm) => {
  return request({
    url: '/tianxin/machineToolInfo',
    method: 'post',
    data: data
  });
};

/**
 * 修改机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等
 * @param data
 */
export const updateMachineToolInfo = (data: MachineToolInfoForm) => {
  return request({
    url: '/tianxin/machineToolInfo',
    method: 'put',
    data: data
  });
};

/**
 * 删除机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等
 * @param id
 */
export const delMachineToolInfo = (id: string | number | Array<string | number>) => {
  return request({
    url: '/tianxin/machineToolInfo/' + id,
    method: 'delete'
  });
};
