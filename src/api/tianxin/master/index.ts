import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { MasterVO, MasterForm, MasterQuery } from '@/api/tianxin/master/types';

/**
 * 查询PMC订单变更管理列表
 * @param query
 * @returns {*}
 */

export const listMaster = (query?: MasterQuery): AxiosPromise<MasterVO[]> => {
  return request({
    url: '/tianxin/master/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询PMC订单变更管理详细
 * @param id
 */
export const getMaster = (id: string | number): AxiosPromise<MasterVO> => {
  return request({
    url: '/tianxin/master/' + id,
    method: 'get'
  });
};

/**
 * 新增PMC订单变更管理
 * @param data
 */
export const addMaster = (data: MasterForm) => {
  return request({
    url: '/tianxin/master',
    method: 'post',
    data: data
  });
};

/**
 * 修改PMC订单变更管理
 * @param data
 */
export const updateMaster = (data: MasterForm) => {
  return request({
    url: '/tianxin/master',
    method: 'put',
    data: data
  });
};

/**
 * 删除PMC订单变更管理
 * @param id
 */
export const delMaster = (id: string | number | Array<string | number>) => {
  return request({
    url: '/tianxin/master/' + id,
    method: 'delete'
  });
};

/**
 * 提交处理方式
 * @param data
 */
export const submitProcessMethod = (data: { queryParams: MasterQuery; processMethod: string }) => {
  return request({
    url: '/tianxin/master/submit',
    method: 'post',
    data: data
  });
};
