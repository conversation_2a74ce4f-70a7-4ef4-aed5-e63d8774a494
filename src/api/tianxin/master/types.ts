export interface MasterVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 订单状态
   */
  sta: string;

  /**
   * 备注信息
   */
  rem: string;

  /**
   * 接单日期
   */
  orderDate: string;

  /**
   * 客户要求交期
   */
  customerReqDeliveryDate: string;

  /**
   * 承诺交期
   */
  promisedDeliveryDate: string;

  /**
   * 下计划日期
   */
  nextPlanDate: string;

  /**
   * 要求完成日期
   */
  requiredCompletionDate: string;

  /**
   * 末工序时间
   */
  lastProcessTime: string;

  /**
   * 入仓日期
   */
  warehouseEntryDate: string;

  /**
   * 实际交期
   */
  actualDeliveryDate: string;

  /**
   * 客户代码
   */
  customerCode: string;

  /**
   * 项目号
   */
  projectNo: string;

  /**
   * 项目经理
   */
  projectManager: string;

  /**
   * 客户PO号
   */
  customerPoNo: string;

  /**
   * PO_ITM
   */
  poItm: string;

  /**
   * SO号
   */
  soNo: string;

  /**
   * SO项次
   */
  soItm: string;

  /**
   * MO_NO
   */
  moNo: string;

  /**
   * 上层订单号
   */
  parentOrderNo: string;

  /**
   * 零件分类
   */
  partClassification: string;

  /**
   * 品号
   */
  prdNo: string;

  /**
   * 图号
   */
  dwgNo: string;

  /**
   * 版本
   */
  version: string;

  /**
   * 客户产品料号
   */
  customerProductNo: string;

  /**
   * 客户产品名称
   */
  customerProductName: string;

  /**
   * 产品描述
   */
  productDescription: string;

  /**
   * 订单数量
   */
  orderQuantity: number;

  /**
   * 生产数量
   */
  productionQuantity: number;

  /**
   * 工艺数量
   */
  processQuantity: number;

  /**
   * 报废数量
   */
  scrapQuantity: number;

  /**
   * 已入仓数
   */
  inWarehouseCount: number;

  /**
   * 欠入仓数
   */
  outstandingWarehouseCount: number;

  /**
   * 交货数量
   */
  deliveryQuantity: number;

  /**
   * 欠出货数量
   */
  outstandingShipmentQuantity: number;

  /**
   * 单件净重
   */
  singlePieceNetWeight: number;

  /**
   * 单位
   */
  unit: string;

  /**
   * 行业
   */
  industry: string;

  /**
   * 订单类别
   */
  orderCategory: string;

  /**
   * 计划类型
   */
  planType: string;

  /**
   * 下单员
   */
  orderPlacer: string;

  /**
   * 跟单员
   */
  followUpStaff: string;

  /**
   * BOM负责人
   */
  bomManager: string;

  /**
   * 工艺编制人
   */
  processCompiler: string;

  /**
   * 合单标识
   */
  mergeFlag: string;

  /**
   * 急单标识
   */
  urgentFlag: string;

  /**
   * 打样标识
   */
  sampleFlag: string;

  /**
   * 是否有标准件
   */
  hasStandardParts: string;

  /**
   * 是否买料
   */
  isMaterialPurchased: string;

  /**
   * 是否电镀
   */
  isElectroplated: string;

  /**
   * 动作
   */
  action: string;

  /**
   * 当前工序
   */
  currentProcess: string;

  /**
   * 加工材料
   */
  processingMaterial: string;

  /**
   * 要求材料
   */
  requiredMaterial: string;

  /**
   * 报价材料
   */
  quotedMaterial: string;

  /**
   * 图纸难度等级
   */
  drawingDifficultyLevel: string;

  /**
   * 工艺难度等级
   */
  processDifficultyLevel: string;

  /**
   * 工艺版本
   */
  processVersion: string;

  /**
   * 完成部门 (供应商)
   */
  completionDepartment: string;

  /**
   * 生产车间 (加工车间)
   */
  productionWorkshop: string;

  /**
   * 利润中心
   */
  profitCenter: string;

  /**
   * 成本中心
   */
  costCenter: string;

  /**
   * 收费备注
   */
  chargeRemarks: string;

  /**
   * 关单原因
   */
  closeOrderReason: string;

  /**
   * 状态1
   */
  sta1: number;

  /**
   * SO单 (同步标记)
   */
  soDocument: string;

  /**
   * MO号 (同步标记)
   */
  moDocument: string;

  /**
   * PMC分组 (同步标记)
   */
  pmcGroup: string;

  /**
   * 工艺 (同步标记)
   */
  process: string;

  /**
   * 排产 (同步标记)
   */
  productionScheduling: string;

  /**
   * MRP (同步标记)
   */
  mrp: string;

  /**
   * 请购 (同步标记)
   */
  purchaseRequest: string;

  /**
   * 下采购单 (同步标记)
   */
  purchaseOrder: string;

  /**
   * 材料收货 (同步标记)
   */
  materialReceipt: string;

  /**
   * IQC检测 (同步标记)
   */
  iqcInspection: string;

  /**
   * 材料退货 (同步标记)
   */
  materialReturn: string;

  /**
   * 材料进仓 (同步标记)
   */
  materialInbound: string;

  /**
   * 托工 (同步标记)
   */
  subcontracting: string;

  /**
   * 请购单号 (同步天心单号)
   */
  purchaseRequestNo: string;

  /**
   * 采购单号 (同步天心单号)
   */
  purchaseOrderNo: string;

  /**
   * 入库单号 (同步天心单号)
   */
  warehouseEntryNo: string;

  /**
   * 出库单号 (同步天心单号)
   */
  warehouseExitNo: string;

  /**
   * 销货单号 (同步天心单号)
   */
  salesOrderNo: string;
}

export interface MasterForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 订单状态
   */
  sta?: string;

  /**
   * 备注信息
   */
  rem?: string;

  /**
   * 接单日期
   */
  orderDate?: string;

  /**
   * 客户要求交期
   */
  customerReqDeliveryDate?: string;

  /**
   * 承诺交期
   */
  promisedDeliveryDate?: string;

  /**
   * 下计划日期
   */
  nextPlanDate?: string;

  /**
   * 要求完成日期
   */
  requiredCompletionDate?: string;

  /**
   * 末工序时间
   */
  lastProcessTime?: string;

  /**
   * 入仓日期
   */
  warehouseEntryDate?: string;

  /**
   * 实际交期
   */
  actualDeliveryDate?: string;

  /**
   * 客户代码
   */
  customerCode?: string;

  /**
   * 项目号
   */
  projectNo?: string;

  /**
   * 项目经理
   */
  projectManager?: string;

  /**
   * 客户PO号
   */
  customerPoNo?: string;

  /**
   * PO_ITM
   */
  poItm?: string;

  /**
   * SO号
   */
  soNo?: string;

  /**
   * SO项次
   */
  soItm?: string;

  /**
   * MO_NO
   */
  moNo?: string;

  /**
   * 上层订单号
   */
  parentOrderNo?: string;

  /**
   * 零件分类
   */
  partClassification?: string;

  /**
   * 品号
   */
  prdNo?: string;

  /**
   * 图号
   */
  dwgNo?: string;

  /**
   * 版本
   */
  version?: string;

  /**
   * 客户产品料号
   */
  customerProductNo?: string;

  /**
   * 客户产品名称
   */
  customerProductName?: string;

  /**
   * 产品描述
   */
  productDescription?: string;

  /**
   * 订单数量
   */
  orderQuantity?: number;

  /**
   * 生产数量
   */
  productionQuantity?: number;

  /**
   * 工艺数量
   */
  processQuantity?: number;

  /**
   * 报废数量
   */
  scrapQuantity?: number;

  /**
   * 已入仓数
   */
  inWarehouseCount?: number;

  /**
   * 欠入仓数
   */
  outstandingWarehouseCount?: number;

  /**
   * 交货数量
   */
  deliveryQuantity?: number;

  /**
   * 欠出货数量
   */
  outstandingShipmentQuantity?: number;

  /**
   * 单件净重
   */
  singlePieceNetWeight?: number;

  /**
   * 单位
   */
  unit?: string;

  /**
   * 行业
   */
  industry?: string;

  /**
   * 订单类别
   */
  orderCategory?: string;

  /**
   * 计划类型
   */
  planType?: string;

  /**
   * 下单员
   */
  orderPlacer?: string;

  /**
   * 跟单员
   */
  followUpStaff?: string;

  /**
   * BOM负责人
   */
  bomManager?: string;

  /**
   * 工艺编制人
   */
  processCompiler?: string;

  /**
   * 合单标识
   */
  mergeFlag?: string;

  /**
   * 急单标识
   */
  urgentFlag?: string;

  /**
   * 打样标识
   */
  sampleFlag?: string;

  /**
   * 是否有标准件
   */
  hasStandardParts?: string;

  /**
   * 是否买料
   */
  isMaterialPurchased?: string;

  /**
   * 是否电镀
   */
  isElectroplated?: string;

  /**
   * 动作
   */
  action?: string;

  /**
   * 当前工序
   */
  currentProcess?: string;

  /**
   * 加工材料
   */
  processingMaterial?: string;

  /**
   * 要求材料
   */
  requiredMaterial?: string;

  /**
   * 报价材料
   */
  quotedMaterial?: string;

  /**
   * 图纸难度等级
   */
  drawingDifficultyLevel?: string;

  /**
   * 工艺难度等级
   */
  processDifficultyLevel?: string;

  /**
   * 工艺版本
   */
  processVersion?: string;

  /**
   * 完成部门 (供应商)
   */
  completionDepartment?: string;

  /**
   * 生产车间 (加工车间)
   */
  productionWorkshop?: string;

  /**
   * 利润中心
   */
  profitCenter?: string;

  /**
   * 成本中心
   */
  costCenter?: string;

  /**
   * 收费备注
   */
  chargeRemarks?: string;

  /**
   * 关单原因
   */
  closeOrderReason?: string;

  /**
   * 状态1
   */
  sta1?: number;

  /**
   * SO单 (同步标记)
   */
  soDocument?: string;

  /**
   * MO号 (同步标记)
   */
  moDocument?: string;

  /**
   * PMC分组 (同步标记)
   */
  pmcGroup?: string;

  /**
   * 工艺 (同步标记)
   */
  process?: string;

  /**
   * 排产 (同步标记)
   */
  productionScheduling?: string;

  /**
   * MRP (同步标记)
   */
  mrp?: string;

  /**
   * 请购 (同步标记)
   */
  purchaseRequest?: string;

  /**
   * 下采购单 (同步标记)
   */
  purchaseOrder?: string;

  /**
   * 材料收货 (同步标记)
   */
  materialReceipt?: string;

  /**
   * IQC检测 (同步标记)
   */
  iqcInspection?: string;

  /**
   * 材料退货 (同步标记)
   */
  materialReturn?: string;

  /**
   * 材料进仓 (同步标记)
   */
  materialInbound?: string;

  /**
   * 托工 (同步标记)
   */
  subcontracting?: string;

  /**
   * 请购单号 (同步天心单号)
   */
  purchaseRequestNo?: string;

  /**
   * 采购单号 (同步天心单号)
   */
  purchaseOrderNo?: string;

  /**
   * 入库单号 (同步天心单号)
   */
  warehouseEntryNo?: string;

  /**
   * 出库单号 (同步天心单号)
   */
  warehouseExitNo?: string;

  /**
   * 销货单号 (同步天心单号)
   */
  salesOrderNo?: string;
}

export interface MasterQuery extends PageQuery {
  /**
   * 订单状态
   */
  sta?: string;

  /**
   * 客户代码
   */
  customerCode?: string;

  /**
   * 客户PO号
   */
  customerPoNo?: string;

  /**
   * MO_NO
   */
  moNo?: string;

  /**
   * 图号
   */
  dwgNo?: string;

  /**
   * 完工状态
   */
  finishStatus?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
