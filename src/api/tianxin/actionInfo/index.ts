import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ActionInfoVO, ActionInfoForm, ActionInfoQuery } from '@/api/tianxin/actionInfo/types';

/**
 * 查询机床扫描查询列表
 * @param query
 * @returns {*}
 */

export const listActionInfo = (query?: ActionInfoQuery): AxiosPromise<ActionInfoVO[]> => {
  return request({
    url: '/tianxin/actionInfo/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询机床扫描查询详细
 * @param id
 */
export const getActionInfo = (id: string | number): AxiosPromise<ActionInfoVO> => {
  return request({
    url: '/tianxin/actionInfo/' + id,
    method: 'get'
  });
};

/**
 * 新增机床扫描查询
 * @param data
 */
export const addActionInfo = (data: ActionInfoForm) => {
  return request({
    url: '/tianxin/actionInfo',
    method: 'post',
    data: data
  });
};

/**
 * 修改机床扫描查询
 * @param data
 */
export const updateActionInfo = (data: ActionInfoForm) => {
  return request({
    url: '/tianxin/actionInfo',
    method: 'put',
    data: data
  });
};

/**
 * 删除机床扫描查询
 * @param id
 */
export const delActionInfo = (id: string | number | Array<string | number>) => {
  return request({
    url: '/tianxin/actionInfo/' + id,
    method: 'delete'
  });
};

/**
 * 获取当前用户组织动作信息
 */
export const getOrganizationActions = (): AxiosPromise<any> => {
  return request({
    url: '/tianxin/actionInfo/organizationActions',
    method: 'get'
  });
};
