export interface ActionInfoVO {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 动作
   */
  action: string;

  /**
   * 部门
   */
  department: string;

  /**
   * 状态
   */
  status: string;

  /**
   * 序号
   */
  sequenceNo: number;

  /**
   * 条码号
   */
  barcodeNo: string;

  /**
   * 是否控制数量
   */
  isQuantityControlled: number;

  /**
   * 是否输入通过数量
   */
  isPassedQuantity: number;

  /**
   * 是否输入报废数量
   */
  isScrappedQuantity: number;

  /**
   * 是否自带上工序数量
   */
  isPreviousProcess: number;

  /**
   * 是否跳工序
   */
  allowSkipProcess: number;

  /**
   * 是否输入数量
   */
  noQuantity: number;

  /**
   * 操作人工号
   */
  operatorCode: string;

  /**
   * 姓名
   */
  operatorName: string;

  /**
   * 操作时间
   */
  operationTime: string;
}

export interface ActionInfoForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 动作
   */
  action?: string;

  /**
   * 部门
   */
  department?: string;

  /**
   * 状态
   */
  status?: string;

  /**
   * 序号
   */
  sequenceNo?: number;

  /**
   * 条码号
   */
  barcodeNo?: string;

  /**
   * 是否控制数量
   */
  isQuantityControlled?: number;

  /**
   * 是否输入通过数量
   */
  isPassedQuantity?: number;

  /**
   * 是否输入报废数量
   */
  isScrappedQuantity?: number;

  /**
   * 是否自带上工序数量
   */
  isPreviousProcess?: number;

  /**
   * 是否跳工序
   */
  allowSkipProcess?: number;

  /**
   * 是否输入数量
   */
  noQuantity?: number;

  /**
   * 操作人工号
   */
  operatorCode?: string;

  /**
   * 姓名
   */
  operatorName?: string;

  /**
   * 操作时间
   */
  operationTime?: string;
}

export interface ActionInfoQuery extends PageQuery {
  /**
   * 动作
   */
  action?: string;

  /**
   * 操作人工号
   */
  operatorCode?: string;

  /**
   * 姓名
   */
  operatorName?: string;

  /**
   * 操作时间
   */
  operationTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
