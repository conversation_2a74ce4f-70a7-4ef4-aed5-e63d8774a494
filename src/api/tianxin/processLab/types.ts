export interface ProcessLabVO {
        /**
         * 主键
         */
            id: string | number;

        /**
         * PRT_SW
         */
            prtSw: string;

        /**
         * PR号
         */
            prNo: string;

        /**
         * PR行
         */
            prItm: string;

        /**
         * 申请类型
         */
            prType: string;

        /**
         * 品号
         */
            prdNo: string;

        /**
         * 描述
         */
            prdDesc: string;

        /**
         * 数量
         */
            qty: number;

        /**
         * 单位
         */
            ut: string;

        /**
         * 请求日期
         */
            prDate: string;

        /**
         * PMC要求日期
         */
            pmcRequestDate: string;

        /**
         * 厂商代号
         */
            vendorCode: string;

        /**
         * 简称
         */
            vendorSnm: string;

        /**
         * 厂商名
         */
            vendorName: string;

        /**
         * 单价
         */
            unitPrice: number;

        /**
         * 货币
         */
            currency: string;

        /**
         * 是否供料
         */
            isProvideMaterials: string | number;

        /**
         * 图号
         */
            dwgNo: string;

        /**
         * PJ号
         */
            pjNo: string;

        /**
         * MO号
         */
            moNo: string;

        /**
         * 订单号
         */
            purchaseNum: string;

        /**
         * 批号
         */
            batchNo: string;

        /**
         * 总价
         */
            sumPrice: number;

        /**
         * 状态
         */
            sta: string;

        /**
         * 外发交期
         */
            outsourcingDate: string;

        /**
         * 客户代码
         */
            custNo: string;

        /**
         * 工序号
         */
            zcNo: string;

        /**
         * 加工中心
         */
            machiningCenter: string;

        /**
         * 工序名称
         */
            zcName: string;

        /**
         * 电镀厂商
         */
            epfSnm: string;

        /**
         * 电镀内容
         */
            electroplateContent: string;

        /**
         * 指定材料
         */
            specifiedMaterials: string;

        /**
         * 是否电镀
         */
            isElectroplate: string;

        /**
         * 电镀厂商代号
         */
            epfCode: string;

        /**
         * 电镀厂商名
         */
            epfName: string;

        /**
         * USR
         */
            usr: string;

        /**
         * SYS_DATE
         */
            sysDate: string;

        /**
         * host
         */
            host: string;

        /**
         * 备注
         */
            rem: string;

}

export interface ProcessLabForm extends BaseEntity {
        /**
         * 主键
         */
            id?: string | number;

        /**
         * PRT_SW
         */
            prtSw?: string;

        /**
         * PR号
         */
            prNo?: string;

        /**
         * PR行
         */
            prItm?: string;

        /**
         * 申请类型
         */
            prType?: string;

        /**
         * 品号
         */
            prdNo?: string;

        /**
         * 描述
         */
            prdDesc?: string;

        /**
         * 数量
         */
            qty?: number;

        /**
         * 单位
         */
            ut?: string;

        /**
         * 请求日期
         */
            prDate?: string;

        /**
         * PMC要求日期
         */
            pmcRequestDate?: string;

        /**
         * 厂商代号
         */
            vendorCode?: string;

        /**
         * 简称
         */
            vendorSnm?: string;

        /**
         * 厂商名
         */
            vendorName?: string;

        /**
         * 单价
         */
            unitPrice?: number;

        /**
         * 货币
         */
            currency?: string;

        /**
         * 是否供料
         */
            isProvideMaterials?: string | number;

        /**
         * 图号
         */
            dwgNo?: string;

        /**
         * PJ号
         */
            pjNo?: string;

        /**
         * MO号
         */
            moNo?: string;

        /**
         * 订单号
         */
            purchaseNum?: string;

        /**
         * 批号
         */
            batchNo?: string;

        /**
         * 总价
         */
            sumPrice?: number;

        /**
         * 状态
         */
            sta?: string;

        /**
         * 外发交期
         */
            outsourcingDate?: string;

        /**
         * 客户代码
         */
            custNo?: string;

        /**
         * 工序号
         */
            zcNo?: string;

        /**
         * 加工中心
         */
            machiningCenter?: string;

        /**
         * 工序名称
         */
            zcName?: string;

        /**
         * 电镀厂商
         */
            epfSnm?: string;

        /**
         * 电镀内容
         */
            electroplateContent?: string;

        /**
         * 指定材料
         */
            specifiedMaterials?: string;

        /**
         * 是否电镀
         */
            isElectroplate?: string;

        /**
         * 电镀厂商代号
         */
            epfCode?: string;

        /**
         * 电镀厂商名
         */
            epfName?: string;

        /**
         * USR
         */
            usr?: string;

        /**
         * SYS_DATE
         */
            sysDate?: string;

        /**
         * host
         */
            host?: string;

        /**
         * 备注
         */
            rem?: string;

}

export interface ProcessLabQuery extends PageQuery {

        /**
         * PRT_SW
         */
            prtSw?: string;

        /**
         * PR号
         */
            prNo?: string;

        /**
         * PR行
         */
            prItm?: string;

        /**
         * 申请类型
         */
            prType?: string;

        /**
         * 品号
         */
            prdNo?: string;

        /**
         * 描述
         */
            prdDesc?: string;

        /**
         * 数量
         */
            qty?: number;

        /**
         * 单位
         */
            ut?: string;

        /**
         * 请求日期
         */
            prDate?: string;

        /**
         * PMC要求日期
         */
            pmcRequestDate?: string;

        /**
         * 厂商代号
         */
            vendorCode?: string;

        /**
         * 简称
         */
            vendorSnm?: string;

        /**
         * 厂商名
         */
            vendorName?: string;

        /**
         * 单价
         */
            unitPrice?: number;

        /**
         * 货币
         */
            currency?: string;

        /**
         * 是否供料
         */
            isProvideMaterials?: string | number;

        /**
         * 图号
         */
            dwgNo?: string;

        /**
         * PJ号
         */
            pjNo?: string;

        /**
         * MO号
         */
            moNo?: string;

        /**
         * 订单号
         */
            purchaseNum?: string;

        /**
         * 批号
         */
            batchNo?: string;

        /**
         * 总价
         */
            sumPrice?: number;

        /**
         * 状态
         */
            sta?: string;

        /**
         * 外发交期
         */
            outsourcingDate?: string;

        /**
         * 客户代码
         */
            custNo?: string;

        /**
         * 工序号
         */
            zcNo?: string;

        /**
         * 加工中心
         */
            machiningCenter?: string;

        /**
         * 工序名称
         */
            zcName?: string;

        /**
         * 电镀厂商
         */
            epfSnm?: string;

        /**
         * 电镀内容
         */
            electroplateContent?: string;

        /**
         * 指定材料
         */
            specifiedMaterials?: string;

        /**
         * 是否电镀
         */
            isElectroplate?: string;

        /**
         * 电镀厂商代号
         */
            epfCode?: string;

        /**
         * 电镀厂商名
         */
            epfName?: string;

        /**
         * USR
         */
            usr?: string;

        /**
         * SYS_DATE
         */
            sysDate?: string;

        /**
         * host
         */
            host?: string;

        /**
         * 备注
         */
            rem?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



