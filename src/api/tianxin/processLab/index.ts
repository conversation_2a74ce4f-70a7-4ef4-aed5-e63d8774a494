import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ProcessLabForm, ProcessLabQuery, ProcessLabVO } from '@/api/tianxin/processLab/types';

/**
 * 查询外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等列表
 * @param query
 * @returns {*}
 */

export const listProcessLab = (query?: ProcessLabQuery): AxiosPromise<ProcessLabVO[]> => {
  return request({
    url: '/tianxin/processLab/list',
    method: 'get',
    params: query
  });
};

export const getOutSourceList = (query?: any): any => {
  return request({
    url: '/tianxin/processLab/getOutSourceList',
    method: 'get',
    params: query
  });
};

/**
 * 查询外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等详细
 * @param id
 */
export const getProcessLab = (id: string | number): AxiosPromise<ProcessLabVO> => {
  return request({
    url: '/tianxin/processLab/' + id,
    method: 'get'
  });
};

/**
 * 新增外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等
 * @param data
 */
export const addProcessLab = (data: ProcessLabForm) => {
  return request({
    url: '/tianxin/processLab',
    method: 'post',
    data: data
  });
};

/**
 * 修改外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等
 * @param data
 */
export const updateProcessLab = (data: ProcessLabForm) => {
  return request({
    url: '/tianxin/processLab',
    method: 'put',
    data: data
  });
};

/**
 * 删除外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等
 * @param id
 */
export const delProcessLab = (id: string | number | Array<string | number>) => {
  return request({
    url: '/tianxin/processLab/' + id,
    method: 'delete'
  });
};

/**
 * 发送邮件 - 发送外发加工标签数据邮件
 * @param data 邮件数据，包含文件和内容
 */
export const sendProcessLabEmail = (data: FormData) => {
  return request({
    url: '/tianxin/processLab/sendEmail',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};
