export interface RecordLogVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * MO号
   */
  moNo: string;

  /**
   * 工序号
   */
  processNo: number;

  /**
   * 工序名称
   */
  processName: string;

  /**
   * 操作员
   */
  operator: string;

  /**
   * 姓名
   */
  operatorName: string;

  /**
   * 机台号
   */
  machineNo: string;

  /**
   * 动作
   */
  action: string;

  /**
   * 开始时间
   */
  startTime: string;

  /**
   * 结束时间
   */
  endTime: string;

  /**
   * 通过数量
   */
  passedQuantity: number;

  /**
   * 报废数量
   */
  scrappedQuantity: number;

  /**
   * 总进度
   */
  totalProgress: number;

  /**
   * 当前进度
   */
  currentProgress: number;

  /**
   * 工艺工时
   */
  processManHours: number;

  /**
   * 实际时间
   */
  actualTime: number;

  /**
   * 备注
   */
  remark: string;

  /**
   * 客户代码
   */
  customerCode: string;

  /**
   * 供应商
   */
  supplier: string;

  /**
   * 储位
   */
  storageLocation: string;

  /**
   * 品号
   */
  partNo: string;

  /**
   * 图号
   */
  drawingNo: string;

  /**
   * 物料描述
   */
  materialDescription: string;

  /**
   * 客户产品料号
   */
  customerProductNo: string;

  /**
   * 客户产品名称
   */
  customerProductName: string;

  /**
   * 批号
   */
  batchNo: string;

  /**
   * 电脑名
   */
  computerName: string;

  /**
   * 行业
   */
  industry: string;

  /**
   * 排产工时
   */
  productionScheduleManHours: number;

  /**
   * 创建时间
   */
  creationTime: string;

}

export interface RecordLogForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * MO号
   */
  moNo?: string;

  /**
   * 工序号
   */
  processNo?: number;

  /**
   * 工序名称
   */
  processName?: string;

  /**
   * 操作员
   */
  operator?: string;

  /**
   * 姓名
   */
  operatorName?: string;

  /**
   * 机台号
   */
  machineNo?: string;

  /**
   * 动作
   */
  action?: string;

  /**
   * 开始时间
   */
  startTime?: string;

  /**
   * 结束时间
   */
  endTime?: string;

  /**
   * 通过数量
   */
  passedQuantity?: number;

  /**
   * 报废数量
   */
  scrappedQuantity?: number;

  /**
   * 总进度
   */
  totalProgress?: number;

  /**
   * 当前进度
   */
  currentProgress?: number;

  /**
   * 工艺工时
   */
  processManHours?: number;

  /**
   * 实际时间
   */
  actualTime?: number;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 客户代码
   */
  customerCode?: string;

  /**
   * 供应商
   */
  supplier?: string;

  /**
   * 储位
   */
  storageLocation?: string;

  /**
   * 品号
   */
  partNo?: string;

  /**
   * 图号
   */
  drawingNo?: string;

  /**
   * 物料描述
   */
  materialDescription?: string;

  /**
   * 客户产品料号
   */
  customerProductNo?: string;

  /**
   * 客户产品名称
   */
  customerProductName?: string;

  /**
   * 批号
   */
  batchNo?: string;

  /**
   * 电脑名
   */
  computerName?: string;

  /**
   * 行业
   */
  industry?: string;

  /**
   * 排产工时
   */
  productionScheduleManHours?: number;

  /**
   * 创建时间
   */
  creationTime?: string;

}

export interface RecordLogQuery extends PageQuery {

  /**
   * MO号
   */
  moNo?: string;

  /**
   * 工序号
   */
  processNo?: number;

  /**
   * 工序名称
   */
  processName?: string;

  /**
   * 操作员
   */
  operator?: string;

  /**
   * 姓名
   */
  operatorName?: string;

  /**
   * 机台号
   */
  machineNo?: string;

  /**
   * 动作
   */
  action?: string;

  /**
   * 开始时间
   */
  startTime?: string;

  /**
   * 结束时间
   */
  endTime?: string;

  /**
   * 通过数量
   */
  passedQuantity?: number;

  /**
   * 报废数量
   */
  scrappedQuantity?: number;

  /**
   * 总进度
   */
  totalProgress?: number;

  /**
   * 当前进度
   */
  currentProgress?: number;

  /**
   * 工艺工时
   */
  processManHours?: number;

  /**
   * 实际时间
   */
  actualTime?: number;

  /**
   * 客户代码
   */
  customerCode?: string;

  /**
   * 供应商
   */
  supplier?: string;

  /**
   * 储位
   */
  storageLocation?: string;

  /**
   * 品号
   */
  partNo?: string;

  /**
   * 图号
   */
  drawingNo?: string;

  /**
   * 物料描述
   */
  materialDescription?: string;

  /**
   * 客户产品料号
   */
  customerProductNo?: string;

  /**
   * 客户产品名称
   */
  customerProductName?: string;

  /**
   * 批号
   */
  batchNo?: string;

  /**
   * 电脑名
   */
  computerName?: string;

  /**
   * 行业
   */
  industry?: string;

  /**
   * 排产工时
   */
  productionScheduleManHours?: number;

  /**
   * 创建时间
   */
  creationTime?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



