import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { RecordLogVO, RecordLogForm, RecordLogQuery } from '@/api/tianxin/scan/types';

/**
 * 查询扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息列表
 * @param query
 * @returns {*}
 */

export const listRecordLog = (query?: RecordLogQuery): AxiosPromise<RecordLogVO[]> => {
  return request({
    url: '/system/recordLog/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息详细
 * @param id
 */
export const getRecordLog = (id: string | number): AxiosPromise<RecordLogVO> => {
  return request({
    url: '/system/recordLog/' + id,
    method: 'get'
  });
};

/**
 * 新增扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息
 * @param data
 */
export const addRecordLog = (data: RecordLogForm) => {
  return request({
    url: '/system/recordLog',
    method: 'post',
    data: data
  });
};

/**
 * 修改扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息
 * @param data
 */
export const updateRecordLog = (data: RecordLogForm) => {
  return request({
    url: '/system/recordLog',
    method: 'put',
    data: data
  });
};

/**
 * 删除扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息
 * @param id
 */
export const delRecordLog = (id: string | number | Array<string | number>) => {
  return request({
    url: '/system/recordLog/' + id,
    method: 'delete'
  });
};
