variables:
  GIT_IMAGE: dockerhub.world-machining.com/alpine/git:v2.49.1
  APP_URL: gitlab.world-machining.com/world/world-cloud-plus-app.git
  HARBOR_PROJECT: world-cloud-test
  DEPLOY_FILE: path_to_deploy_file

stages:
  - build_npm
  - push_docker
  - update_deploy

build_npm:
  stage: build_npm
  variables:
    GIT_STRATEGY: fetch
  # 正式环境，手动触发
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
      when: manual
  script:
    - VERSION=$(cat version | perl -pe 's/\s+//')
    - IMAGE=$HARBOR_HOST/$HARBOR_PROJECT/world-cloud-ui:$VERSION
    - echo "VERSION=$VERSION" >> image.env
    - echo "IMAGE=$IMAGE" >> image.env
    - docker build -t $IMAGE .
  artifacts:
    reports:
      dotenv: image.env

push_docker:
  stage: push_docker
  variables:
    GIT_STRATEGY: none
  dependencies:
    - build_npm
  script:
    - docker login $HARBOR_HOST -u $HARBOR_USERNAME -p $HARBOR_PASSWORD
    - docker push $IMAGE

update_deploy:
  stage: update_deploy
  dependencies:
    - build_npm
  image: $GIT_IMAGE
  before_script:
    - if [ -z "$IMAGE" ]; then exit 1; fi
  script:
    - |
      # 配置 Git 全局信息
      git config --global user.email "<EMAIL>"
      git config --global user.name "gitlab_cicd"

    - |
      # 克隆目标仓库
      TARGET_REPO_URL="https://gitlab_cicd:$APP_TOKEN@$APP_URL"
      git clone "$TARGET_REPO_URL" deploy_repo
      cd deploy_repo

    - |
      # 修改镜像版本号
      deploy_file=$DEPLOY_FILE
      perl -i -pe "s#$HARBOR_HOST/$HARBOR_PROJECT/world-cloud-ui:([\w\.]+)#$IMAGE#" $deploy_file

    - |
      # 提交并推送
      git add .
      git commit -m "update version to $VERSION"
      git push origin master
