from dockerhub.world-machining.com/library/node:20.19.5 AS build
WORKDIR /app

COPY package*.json ./
RUN npm config set registry https://registry.npmmirror.com
RUN npm install
COPY . .
RUN node --max_old_space_size=1024000 ./node_modules/vite/bin/vite.js build --mode development
RUN ls /app/dist

FROM dockerhub.world-machining.com/library/nginx:1.28-alpine
COPY --from=build /app/dist /usr/share/nginx/html/
EXPOSE 80
COPY ./nginx.conf /etc/nginx/nginx.conf
CMD ["nginx", "-g", "daemon off;"]
